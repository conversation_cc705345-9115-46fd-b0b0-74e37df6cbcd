import { shopPortalService } from '../../services/shopPortalService';
import { apiService } from '../../services/api';
import { DeliveryNote, DeliveryNoteItem } from '../../services/shopPortalService';

// Mock the API service
jest.mock('../../services/api');
const mockApiService = apiService as jest.Mocked<typeof apiService>;

describe('Delivery Receiving Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock stored credentials
    mockApiService.getStoredCredentials.mockReturnValue({
      companyId: 'test-company-id',
      locationId: 'test-location-id'
    });
  });

  const mockDeliveryNote: DeliveryNote = {
    id: 'dn-123',
    deliveryNoteNumber: 'DN-000001',
    orderId: 'order-123',
    locationId: 'test-location-id',
    items: [
      {
        itemId: 'item-1',
        name: 'Test Flour',
        orderedQuantity: 10,
        deliveredQuantity: 10,
        receivedQuantity: 0,
        unit: 'kg',
        hasDiscrepancy: false
      },
      {
        itemId: 'item-2',
        name: 'Test Sugar',
        orderedQuantity: 5,
        deliveredQuantity: 4,
        receivedQuantity: 0,
        unit: 'kg',
        hasDiscrepancy: true,
        discrepancyNotes: 'One bag damaged'
      }
    ],
    deliveryDate: new Date('2024-01-15'),
    status: 'delivered',
    notes: 'Delivery completed',
    createdAt: new Date('2024-01-14'),
    updatedAt: new Date('2024-01-15')
  };

  describe('getPendingDeliveries', () => {
    it('should fetch pending deliveries for location', async () => {
      const mockResponse = {
        success: true,
        data: [mockDeliveryNote]
      };

      mockApiService.get.mockResolvedValue(mockResponse);

      const result = await shopPortalService.getPendingDeliveries();

      expect(mockApiService.get).toHaveBeenCalledWith(
        '/company/test-company-id/shop-portal/deliveries?locationId=test-location-id&status=delivered'
      );
      expect(result).toEqual(mockResponse);
    });

    it('should throw error when credentials are missing', async () => {
      mockApiService.getStoredCredentials.mockReturnValue({
        companyId: null,
        locationId: null
      });

      await expect(shopPortalService.getPendingDeliveries()).rejects.toThrow(
        'Company ID and Location ID are required'
      );
    });
  });

  describe('getDeliveryNote', () => {
    it('should fetch specific delivery note', async () => {
      const deliveryNoteId = 'dn-123';
      const mockResponse = {
        success: true,
        data: mockDeliveryNote
      };

      mockApiService.get.mockResolvedValue(mockResponse);

      const result = await shopPortalService.getDeliveryNote(deliveryNoteId);

      expect(mockApiService.get).toHaveBeenCalledWith(
        '/company/test-company-id/shop-portal/deliveries/dn-123'
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe('receiveDelivery', () => {
    it('should receive delivery with all items confirmed', async () => {
      const deliveryNoteId = 'dn-123';
      const receivingData = {
        receivedItems: [
          {
            itemId: 'item-1',
            receivedQuantity: 10,
            hasDiscrepancy: false
          },
          {
            itemId: 'item-2',
            receivedQuantity: 4,
            hasDiscrepancy: true,
            discrepancyNotes: 'Confirmed - one bag damaged'
          }
        ],
        receivedBy: 'John Doe',
        notes: 'All items received and checked',
        signature: 'base64-signature-data'
      };

      const mockResponse = {
        success: true,
        data: {
          ...mockDeliveryNote,
          status: 'received',
          receivedDate: new Date(),
          receivedBy: 'John Doe'
        }
      };

      mockApiService.patch.mockResolvedValue(mockResponse);

      const result = await shopPortalService.receiveDelivery(deliveryNoteId, receivingData);

      expect(mockApiService.patch).toHaveBeenCalledWith(
        '/company/test-company-id/shop-portal/deliveries/dn-123/receive',
        receivingData
      );
      expect(result).toEqual(mockResponse);
    });

    it('should handle partial delivery receiving', async () => {
      const deliveryNoteId = 'dn-123';
      const receivingData = {
        receivedItems: [
          {
            itemId: 'item-1',
            receivedQuantity: 8, // Less than delivered
            hasDiscrepancy: true,
            discrepancyNotes: 'Two bags missing'
          },
          {
            itemId: 'item-2',
            receivedQuantity: 4,
            hasDiscrepancy: false
          }
        ],
        receivedBy: 'Jane Smith',
        notes: 'Partial delivery - some items missing',
        signature: 'base64-signature-data'
      };

      const mockResponse = {
        success: true,
        data: {
          ...mockDeliveryNote,
          status: 'received',
          receivedDate: new Date(),
          receivedBy: 'Jane Smith'
        }
      };

      mockApiService.patch.mockResolvedValue(mockResponse);

      const result = await shopPortalService.receiveDelivery(deliveryNoteId, receivingData);

      expect(result.success).toBe(true);
      expect(mockApiService.patch).toHaveBeenCalledWith(
        '/company/test-company-id/shop-portal/deliveries/dn-123/receive',
        receivingData
      );
    });

    it('should handle delivery rejection', async () => {
      const deliveryNoteId = 'dn-123';
      const rejectionData = {
        receivedItems: [],
        receivedBy: 'Store Manager',
        notes: 'Delivery rejected - items damaged',
        signature: 'base64-signature-data',
        rejected: true,
        rejectionReason: 'All items damaged during transport'
      };

      const mockResponse = {
        success: true,
        data: {
          ...mockDeliveryNote,
          status: 'rejected',
          receivedDate: new Date(),
          receivedBy: 'Store Manager'
        }
      };

      mockApiService.patch.mockResolvedValue(mockResponse);

      const result = await shopPortalService.receiveDelivery(deliveryNoteId, rejectionData);

      expect(result.success).toBe(true);
      expect(mockApiService.patch).toHaveBeenCalledWith(
        '/company/test-company-id/shop-portal/deliveries/dn-123/receive',
        rejectionData
      );
    });
  });

  describe('updateDeliveryItemDiscrepancy', () => {
    it('should update item discrepancy information', async () => {
      const deliveryNoteId = 'dn-123';
      const itemId = 'item-1';
      const discrepancyData = {
        hasDiscrepancy: true,
        discrepancyNotes: 'Quality issue - items expired',
        reportedQuantity: 8
      };

      const mockResponse = {
        success: true,
        data: {
          ...mockDeliveryNote,
          items: mockDeliveryNote.items.map(item => 
            item.itemId === itemId 
              ? { ...item, ...discrepancyData }
              : item
          )
        }
      };

      mockApiService.patch.mockResolvedValue(mockResponse);

      const result = await shopPortalService.updateDeliveryItemDiscrepancy(
        deliveryNoteId, 
        itemId, 
        discrepancyData
      );

      expect(mockApiService.patch).toHaveBeenCalledWith(
        '/company/test-company-id/shop-portal/deliveries/dn-123/items/item-1/discrepancy',
        discrepancyData
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe('getDeliveryHistory', () => {
    it('should fetch delivery history with filters', async () => {
      const filters = {
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-01-31'),
        status: 'received'
      };

      const mockResponse = {
        success: true,
        data: {
          deliveries: [mockDeliveryNote],
          pagination: {
            page: 1,
            limit: 10,
            total: 1,
            totalPages: 1
          }
        }
      };

      mockApiService.get.mockResolvedValue(mockResponse);

      const result = await shopPortalService.getDeliveryHistory(filters);

      expect(mockApiService.get).toHaveBeenCalledWith(
        '/company/test-company-id/shop-portal/deliveries/history?locationId=test-location-id&startDate=2024-01-01T00%3A00%3A00.000Z&endDate=2024-01-31T00%3A00%3A00.000Z&status=received'
      );
      expect(result).toEqual(mockResponse);
    });

    it('should fetch delivery history without filters', async () => {
      const mockResponse = {
        success: true,
        data: {
          deliveries: [],
          pagination: {
            page: 1,
            limit: 10,
            total: 0,
            totalPages: 0
          }
        }
      };

      mockApiService.get.mockResolvedValue(mockResponse);

      const result = await shopPortalService.getDeliveryHistory();

      expect(mockApiService.get).toHaveBeenCalledWith(
        '/company/test-company-id/shop-portal/deliveries/history?locationId=test-location-id'
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe('Delivery Validation', () => {
    it('should validate delivery note items before receiving', () => {
      const deliveryNote = mockDeliveryNote;
      const receivingData = {
        receivedItems: [
          {
            itemId: 'item-1',
            receivedQuantity: 10,
            hasDiscrepancy: false
          },
          {
            itemId: 'item-2',
            receivedQuantity: 4,
            hasDiscrepancy: false
          }
        ]
      };

      // Validate that all delivery note items are accounted for
      const deliveryItemIds = deliveryNote.items.map(item => item.itemId);
      const receivingItemIds = receivingData.receivedItems.map(item => item.itemId);

      expect(deliveryItemIds.sort()).toEqual(receivingItemIds.sort());
    });

    it('should detect quantity discrepancies', () => {
      const deliveryItem = mockDeliveryNote.items[0];
      const receivedQuantity = 8; // Less than delivered quantity of 10

      const hasDiscrepancy = receivedQuantity !== deliveryItem.deliveredQuantity;
      expect(hasDiscrepancy).toBe(true);
    });

    it('should calculate total received value', () => {
      const receivingData = {
        receivedItems: [
          { itemId: 'item-1', receivedQuantity: 10, unitPrice: 5.50 },
          { itemId: 'item-2', receivedQuantity: 4, unitPrice: 3.25 }
        ]
      };

      const totalValue = receivingData.receivedItems.reduce(
        (sum, item) => sum + (item.receivedQuantity * item.unitPrice), 
        0
      );

      expect(totalValue).toBe(68); // (10 * 5.50) + (4 * 3.25)
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors during delivery receiving', async () => {
      const deliveryNoteId = 'dn-123';
      const receivingData = {
        receivedItems: [],
        receivedBy: 'Test User',
        signature: 'signature-data'
      };

      mockApiService.patch.mockRejectedValue(new Error('Network error'));

      await expect(
        shopPortalService.receiveDelivery(deliveryNoteId, receivingData)
      ).rejects.toThrow('Network error');
    });

    it('should handle invalid delivery note ID', async () => {
      const invalidId = 'invalid-id';

      mockApiService.get.mockResolvedValue({
        success: false,
        error: 'Delivery note not found'
      });

      const result = await shopPortalService.getDeliveryNote(invalidId);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Delivery note not found');
    });

    it('should handle missing signature data', async () => {
      const deliveryNoteId = 'dn-123';
      const receivingDataWithoutSignature = {
        receivedItems: [
          {
            itemId: 'item-1',
            receivedQuantity: 10,
            hasDiscrepancy: false
          }
        ],
        receivedBy: 'Test User'
        // Missing signature
      };

      mockApiService.patch.mockResolvedValue({
        success: false,
        error: 'Signature is required for delivery confirmation'
      });

      const result = await shopPortalService.receiveDelivery(
        deliveryNoteId, 
        receivingDataWithoutSignature
      );

      expect(result.success).toBe(false);
      expect(result.error).toBe('Signature is required for delivery confirmation');
    });
  });
});
