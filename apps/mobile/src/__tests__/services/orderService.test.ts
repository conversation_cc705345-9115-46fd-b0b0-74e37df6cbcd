import { orderService, OrderService } from '../../services/orderService';
import { apiService } from '../../services/api';
import { Order, OrderFilter, OrderSummary } from '../../types';

// Mock the API service
jest.mock('../../services/api');
const mockApiService = apiService as jest.Mocked<typeof apiService>;

describe('OrderService', () => {
  let service: OrderService;

  beforeEach(() => {
    service = new OrderService();
    jest.clearAllMocks();
  });

  describe('getOrders', () => {
    it('should fetch orders without filters', async () => {
      const mockResponse = {
        success: true,
        data: {
          items: [
            {
              id: '1',
              orderNumber: 'ORD-001',
              status: 'confirmed',
              totalAmount: 100,
              orderTime: new Date()
            }
          ],
          pagination: {
            page: 1,
            limit: 10,
            total: 1,
            totalPages: 1
          }
        }
      };

      mockApiService.get.mockResolvedValue(mockResponse);

      const result = await service.getOrders();

      expect(mockApiService.get).toHaveBeenCalledWith('/orders');
      expect(result).toEqual(mockResponse);
    });

    it('should fetch orders with status filter', async () => {
      const filter: OrderFilter = {
        status: ['confirmed', 'pending']
      };

      const mockResponse = {
        success: true,
        data: {
          items: [],
          pagination: { page: 1, limit: 10, total: 0, totalPages: 0 }
        }
      };

      mockApiService.get.mockResolvedValue(mockResponse);

      await service.getOrders(filter);

      expect(mockApiService.get).toHaveBeenCalledWith('/orders?status=confirmed%2Cpending');
    });

    it('should fetch orders with date range filter', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');
      const filter: OrderFilter = {
        dateRange: { start: startDate, end: endDate }
      };

      const mockResponse = {
        success: true,
        data: {
          items: [],
          pagination: { page: 1, limit: 10, total: 0, totalPages: 0 }
        }
      };

      mockApiService.get.mockResolvedValue(mockResponse);

      await service.getOrders(filter);

      expect(mockApiService.get).toHaveBeenCalledWith(
        `/orders?startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`
      );
    });

    it('should fetch orders with multiple filters', async () => {
      const filter: OrderFilter = {
        status: ['confirmed'],
        orderType: ['dine-in'],
        customerName: 'John Doe'
      };

      const mockResponse = {
        success: true,
        data: {
          items: [],
          pagination: { page: 1, limit: 10, total: 0, totalPages: 0 }
        }
      };

      mockApiService.get.mockResolvedValue(mockResponse);

      await service.getOrders(filter);

      expect(mockApiService.get).toHaveBeenCalledWith(
        '/orders?status=confirmed&orderType=dine-in&customerName=John+Doe'
      );
    });
  });

  describe('getOrder', () => {
    it('should fetch a single order by ID', async () => {
      const orderId = 'order-123';
      const mockOrder: Order = {
        id: orderId,
        orderNumber: 'ORD-123',
        status: 'confirmed',
        totalAmount: 150,
        orderTime: new Date(),
        items: [],
        customer: { name: 'Test Customer', phone: '************' }
      };

      const mockResponse = {
        success: true,
        data: mockOrder
      };

      mockApiService.get.mockResolvedValue(mockResponse);

      const result = await service.getOrder(orderId);

      expect(mockApiService.get).toHaveBeenCalledWith('/orders/order-123');
      expect(result).toEqual(mockResponse);
    });
  });

  describe('createOrder', () => {
    it('should create a new order', async () => {
      const newOrder = {
        orderNumber: 'ORD-NEW',
        status: 'pending' as const,
        totalAmount: 200,
        items: [
          {
            id: 'item-1',
            name: 'Test Item',
            quantity: 2,
            price: 100
          }
        ],
        customer: { name: 'New Customer', phone: '************' }
      };

      const mockResponse = {
        success: true,
        data: {
          ...newOrder,
          id: 'new-order-id',
          orderTime: new Date()
        }
      };

      mockApiService.post.mockResolvedValue(mockResponse);

      const result = await service.createOrder(newOrder);

      expect(mockApiService.post).toHaveBeenCalledWith('/orders', newOrder);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('updateOrderStatus', () => {
    it('should update order status', async () => {
      const orderId = 'order-123';
      const newStatus = 'completed';

      const mockResponse = {
        success: true,
        data: {
          id: orderId,
          status: newStatus
        }
      };

      mockApiService.patch.mockResolvedValue(mockResponse);

      const result = await service.updateOrderStatus(orderId, newStatus);

      expect(mockApiService.patch).toHaveBeenCalledWith('/orders/order-123/status', { status: newStatus });
      expect(result).toEqual(mockResponse);
    });
  });

  describe('cancelOrder', () => {
    it('should cancel an order without reason', async () => {
      const orderId = 'order-123';

      const mockResponse = {
        success: true,
        data: {
          id: orderId,
          status: 'cancelled'
        }
      };

      mockApiService.patch.mockResolvedValue(mockResponse);

      const result = await service.cancelOrder(orderId);

      expect(mockApiService.patch).toHaveBeenCalledWith('/orders/order-123/cancel', {});
      expect(result).toEqual(mockResponse);
    });

    it('should cancel an order with reason', async () => {
      const orderId = 'order-123';
      const reason = 'Customer requested cancellation';

      const mockResponse = {
        success: true,
        data: {
          id: orderId,
          status: 'cancelled',
          cancellationReason: reason
        }
      };

      mockApiService.patch.mockResolvedValue(mockResponse);

      const result = await service.cancelOrder(orderId, reason);

      expect(mockApiService.patch).toHaveBeenCalledWith('/orders/order-123/cancel', { reason });
      expect(result).toEqual(mockResponse);
    });
  });

  describe('completeOrder', () => {
    it('should complete an order', async () => {
      const orderId = 'order-123';

      const mockResponse = {
        success: true,
        data: {
          id: orderId,
          status: 'completed'
        }
      };

      mockApiService.patch.mockResolvedValue(mockResponse);

      const result = await service.completeOrder(orderId);

      expect(mockApiService.patch).toHaveBeenCalledWith('/orders/order-123/complete', {});
      expect(result).toEqual(mockResponse);
    });
  });

  describe('getOrderSummary', () => {
    it('should get order summary without date range', async () => {
      const mockSummary: OrderSummary = {
        totalOrders: 50,
        totalRevenue: 5000,
        averageOrderValue: 100,
        topItems: [
          { name: 'Item 1', quantity: 20, revenue: 2000 }
        ]
      };

      const mockResponse = {
        success: true,
        data: mockSummary
      };

      mockApiService.get.mockResolvedValue(mockResponse);

      const result = await service.getOrderSummary();

      expect(mockApiService.get).toHaveBeenCalledWith('/orders/summary');
      expect(result).toEqual(mockResponse);
    });

    it('should get order summary with date range', async () => {
      const dateRange = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      };

      const mockResponse = {
        success: true,
        data: {
          totalOrders: 25,
          totalRevenue: 2500,
          averageOrderValue: 100,
          topItems: []
        }
      };

      mockApiService.get.mockResolvedValue(mockResponse);

      await service.getOrderSummary(dateRange);

      expect(mockApiService.get).toHaveBeenCalledWith(
        `/orders/summary?startDate=${dateRange.start.toISOString()}&endDate=${dateRange.end.toISOString()}`
      );
    });
  });

  describe('getActiveOrders', () => {
    it('should fetch active orders', async () => {
      const mockActiveOrders = [
        {
          id: '1',
          orderNumber: 'ORD-001',
          status: 'in-progress',
          totalAmount: 100,
          orderTime: new Date()
        }
      ];

      const mockResponse = {
        success: true,
        data: mockActiveOrders
      };

      mockApiService.get.mockResolvedValue(mockResponse);

      const result = await service.getActiveOrders();

      expect(mockApiService.get).toHaveBeenCalledWith('/orders/active');
      expect(result).toEqual(mockResponse);
    });
  });

  describe('updateEstimatedCompletion', () => {
    it('should update estimated completion time', async () => {
      const orderId = 'order-123';
      const estimatedCompletion = new Date('2024-01-01T15:30:00Z');

      const mockResponse = {
        success: true,
        data: {
          id: orderId,
          estimatedCompletion
        }
      };

      mockApiService.patch.mockResolvedValue(mockResponse);

      const result = await service.updateEstimatedCompletion(orderId, estimatedCompletion);

      expect(mockApiService.patch).toHaveBeenCalledWith('/orders/order-123/estimated-completion', {
        estimatedCompletion: estimatedCompletion.toISOString()
      });
      expect(result).toEqual(mockResponse);
    });
  });

  describe('assignStaff', () => {
    it('should assign staff to an order', async () => {
      const orderId = 'order-123';
      const staffId = 'staff-456';

      const mockResponse = {
        success: true,
        data: {
          id: orderId,
          assignedStaff: staffId
        }
      };

      mockApiService.patch.mockResolvedValue(mockResponse);

      const result = await service.assignStaff(orderId, staffId);

      expect(mockApiService.patch).toHaveBeenCalledWith('/orders/order-123/assign', { staffId });
      expect(result).toEqual(mockResponse);
    });
  });
});
