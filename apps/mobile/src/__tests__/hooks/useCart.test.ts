import { renderHook, act } from '@testing-library/react';
import { useCart } from '../../hooks/useCart';
import { posService } from '../../services/posService';
import { MenuItem } from '../../types';
import { BranchInventoryItem } from '../../types/api';

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

// Mock posService
jest.mock('../../services/posService');
const mockPosService = posService as jest.Mocked<typeof posService>;

describe('useCart', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
    mockPosService.calculateTax.mockReturnValue(8);
    mockPosService.validateStock.mockResolvedValue({
      isValid: true,
      unavailableItems: []
    });
  });

  const mockMenuItem: MenuItem = {
    id: 'menu-1',
    name: 'Test Burger',
    price: 12.99,
    category: 'Burgers',
    description: 'Delicious test burger',
    isAvailable: true,
    imageUrl: 'test-image.jpg'
  };

  const mockInventoryItem: BranchInventoryItem = {
    id: 'inv-1',
    name: 'Test Ingredient',
    price: 5.50,
    category: 'Ingredients',
    description: 'Test ingredient',
    isAvailable: true,
    image: 'ingredient.jpg',
    stockLevel: 10
  };

  describe('Cart Initialization', () => {
    it('should initialize with empty cart', () => {
      const { result } = renderHook(() => useCart());

      expect(result.current.cartItems).toEqual([]);
      expect(result.current.getTotalItems()).toBe(0);
      expect(result.current.getTotalPrice()).toBe(8); // Just tax
      expect(result.current.getSubtotal()).toBe(0);
    });

    it('should load cart from localStorage on mount', () => {
      const savedCart = [
        {
          id: 'menu-1',
          name: 'Test Burger',
          price: 12.99,
          quantity: 2,
          category: 'Burgers',
          timestamp: Date.now()
        }
      ];
      localStorageMock.getItem.mockReturnValue(JSON.stringify(savedCart));

      const { result } = renderHook(() => useCart());

      expect(result.current.cartItems).toEqual(savedCart);
      expect(result.current.getTotalItems()).toBe(2);
    });

    it('should handle corrupted localStorage data gracefully', () => {
      localStorageMock.getItem.mockReturnValue('invalid-json');
      console.warn = jest.fn(); // Mock console.warn

      const { result } = renderHook(() => useCart());

      expect(result.current.cartItems).toEqual([]);
      expect(console.warn).toHaveBeenCalledWith('Failed to load cart from localStorage:', expect.any(Error));
    });
  });

  describe('Adding Items to Cart', () => {
    it('should add menu item to cart', async () => {
      const { result } = renderHook(() => useCart());

      await act(async () => {
        const success = await result.current.addToCart(mockMenuItem, 2);
        expect(success).toBe(true);
      });

      expect(result.current.cartItems).toHaveLength(1);
      expect(result.current.cartItems[0]).toMatchObject({
        id: 'menu-1',
        name: 'Test Burger',
        price: 12.99,
        quantity: 2,
        category: 'Burgers'
      });
      expect(result.current.getTotalItems()).toBe(2);
      expect(result.current.getSubtotal()).toBe(25.98);
    });

    it('should add inventory item to cart', async () => {
      const { result } = renderHook(() => useCart());

      await act(async () => {
        const success = await result.current.addToCart(mockInventoryItem, 3);
        expect(success).toBe(true);
      });

      expect(result.current.cartItems).toHaveLength(1);
      expect(result.current.cartItems[0]).toMatchObject({
        id: 'inv-1',
        name: 'Test Ingredient',
        price: 5.50,
        quantity: 3,
        stockLevel: 10
      });
    });

    it('should update quantity when adding existing item', async () => {
      const { result } = renderHook(() => useCart());

      await act(async () => {
        await result.current.addToCart(mockMenuItem, 1);
        await result.current.addToCart(mockMenuItem, 2);
      });

      expect(result.current.cartItems).toHaveLength(1);
      expect(result.current.cartItems[0].quantity).toBe(3);
      expect(result.current.getTotalItems()).toBe(3);
    });

    it('should reject adding item when insufficient stock', async () => {
      const { result } = renderHook(() => useCart());

      await act(async () => {
        const success = await result.current.addToCart(mockInventoryItem, 15); // More than stock level of 10
        expect(success).toBe(false);
      });

      expect(result.current.cartItems).toHaveLength(0);
      expect(result.current.cartErrors).toContain('Not enough stock for Test Ingredient. Available: 10');
    });

    it('should reject updating quantity beyond stock level', async () => {
      const { result } = renderHook(() => useCart());

      await act(async () => {
        await result.current.addToCart(mockInventoryItem, 5);
        const success = await result.current.addToCart(mockInventoryItem, 8); // Total would be 13, more than stock of 10
        expect(success).toBe(false);
      });

      expect(result.current.cartItems[0].quantity).toBe(5); // Should remain unchanged
      expect(result.current.cartErrors).toContain('Not enough stock for Test Ingredient. Available: 10');
    });
  });

  describe('Removing Items from Cart', () => {
    it('should remove item completely when quantity is 1', async () => {
      const { result } = renderHook(() => useCart());

      await act(async () => {
        await result.current.addToCart(mockMenuItem, 1);
        result.current.removeFromCart('menu-1');
      });

      expect(result.current.cartItems).toHaveLength(0);
      expect(result.current.getTotalItems()).toBe(0);
    });

    it('should decrease quantity when quantity > 1', async () => {
      const { result } = renderHook(() => useCart());

      await act(async () => {
        await result.current.addToCart(mockMenuItem, 3);
        result.current.removeFromCart('menu-1');
      });

      expect(result.current.cartItems).toHaveLength(1);
      expect(result.current.cartItems[0].quantity).toBe(2);
      expect(result.current.getTotalItems()).toBe(2);
    });

    it('should remove specific item by timestamp', async () => {
      const { result } = renderHook(() => useCart());

      await act(async () => {
        await result.current.addToCart(mockMenuItem, 1);
      });

      const timestamp = result.current.cartItems[0].timestamp;

      await act(async () => {
        result.current.removeFromCart('menu-1', timestamp);
      });

      expect(result.current.cartItems).toHaveLength(0);
    });
  });

  describe('Updating Quantities', () => {
    it('should update item quantity', async () => {
      const { result } = renderHook(() => useCart());

      await act(async () => {
        await result.current.addToCart(mockMenuItem, 2);
        result.current.updateQuantity('menu-1', 5);
      });

      expect(result.current.cartItems[0].quantity).toBe(5);
      expect(result.current.getTotalItems()).toBe(5);
    });

    it('should remove item when quantity is set to 0', async () => {
      const { result } = renderHook(() => useCart());

      await act(async () => {
        await result.current.addToCart(mockMenuItem, 2);
        result.current.updateQuantity('menu-1', 0);
      });

      expect(result.current.cartItems).toHaveLength(0);
    });

    it('should reject quantity update beyond stock level', async () => {
      const { result } = renderHook(() => useCart());

      await act(async () => {
        await result.current.addToCart(mockInventoryItem, 5);
        result.current.updateQuantity('inv-1', 15); // More than stock level of 10
      });

      expect(result.current.cartItems[0].quantity).toBe(5); // Should remain unchanged
      expect(result.current.cartErrors).toContain('Not enough stock for Test Ingredient. Available: 10');
    });
  });

  describe('Cart Calculations', () => {
    it('should calculate subtotal correctly', async () => {
      const { result } = renderHook(() => useCart());

      await act(async () => {
        await result.current.addToCart(mockMenuItem, 2); // 2 * 12.99 = 25.98
        await result.current.addToCart(mockInventoryItem, 3); // 3 * 5.50 = 16.50
      });

      expect(result.current.getSubtotal()).toBe(42.48);
    });

    it('should calculate tax correctly', async () => {
      const { result } = renderHook(() => useCart());

      await act(async () => {
        await result.current.addToCart(mockMenuItem, 1);
      });

      const tax = result.current.getTax(0.1); // 10% tax rate
      expect(mockPosService.calculateTax).toHaveBeenCalledWith(12.99, 0.1);
    });

    it('should calculate total price correctly', async () => {
      const { result } = renderHook(() => useCart());

      await act(async () => {
        await result.current.addToCart(mockMenuItem, 1); // 12.99
      });

      const total = result.current.getTotalPrice();
      expect(total).toBe(20.99); // 12.99 + 8 (mocked tax)
    });

    it('should count total items correctly', async () => {
      const { result } = renderHook(() => useCart());

      await act(async () => {
        await result.current.addToCart(mockMenuItem, 2);
        await result.current.addToCart(mockInventoryItem, 3);
      });

      expect(result.current.getTotalItems()).toBe(5);
    });
  });

  describe('Cart Validation', () => {
    it('should validate cart stock successfully', async () => {
      const { result } = renderHook(() => useCart());

      await act(async () => {
        await result.current.addToCart(mockMenuItem, 2);
      });

      await act(async () => {
        const validation = await result.current.validateCartStock();
        expect(validation.isValid).toBe(true);
        expect(validation.issues).toEqual([]);
      });

      expect(mockPosService.validateStock).toHaveBeenCalledWith([
        { productId: 'menu-1', quantity: 2 }
      ]);
    });

    it('should handle stock validation failure', async () => {
      mockPosService.validateStock.mockResolvedValue({
        isValid: false,
        unavailableItems: [
          { productId: 'menu-1', requestedQuantity: 5, availableStock: 2 }
        ]
      });

      const { result } = renderHook(() => useCart());

      await act(async () => {
        await result.current.addToCart(mockMenuItem, 5);
      });

      await act(async () => {
        const validation = await result.current.validateCartStock();
        expect(validation.isValid).toBe(false);
        expect(validation.issues).toContain('Test Burger: requested 5, available 2');
      });
    });

    it('should return valid for empty cart', async () => {
      const { result } = renderHook(() => useCart());

      await act(async () => {
        const validation = await result.current.validateCartStock();
        expect(validation.isValid).toBe(true);
        expect(validation.issues).toEqual([]);
      });

      expect(mockPosService.validateStock).not.toHaveBeenCalled();
    });
  });

  describe('Cart Management', () => {
    it('should clear cart completely', async () => {
      const { result } = renderHook(() => useCart());

      await act(async () => {
        await result.current.addToCart(mockMenuItem, 2);
        await result.current.addToCart(mockInventoryItem, 1);
        result.current.clearCart();
      });

      expect(result.current.cartItems).toHaveLength(0);
      expect(result.current.cartErrors).toHaveLength(0);
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('pos_cart');
    });

    it('should clear errors', async () => {
      const { result } = renderHook(() => useCart());

      await act(async () => {
        await result.current.addToCart(mockInventoryItem, 15); // This should create an error
        result.current.clearErrors();
      });

      expect(result.current.cartErrors).toHaveLength(0);
    });

    it('should find item in cart', async () => {
      const { result } = renderHook(() => useCart());

      await act(async () => {
        await result.current.addToCart(mockMenuItem, 2);
      });

      const foundItem = result.current.getItemInCart('menu-1');
      expect(foundItem).toBeDefined();
      expect(foundItem?.name).toBe('Test Burger');

      const notFoundItem = result.current.getItemInCart('non-existent');
      expect(notFoundItem).toBeUndefined();
    });
  });

  describe('LocalStorage Integration', () => {
    it('should save cart to localStorage when items change', async () => {
      const { result } = renderHook(() => useCart());

      await act(async () => {
        await result.current.addToCart(mockMenuItem, 1);
      });

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'pos_cart',
        expect.stringContaining('"name":"Test Burger"')
      );
    });

    it('should handle localStorage save errors gracefully', async () => {
      localStorageMock.setItem.mockImplementation(() => {
        throw new Error('Storage quota exceeded');
      });
      console.warn = jest.fn();

      const { result } = renderHook(() => useCart());

      await act(async () => {
        await result.current.addToCart(mockMenuItem, 1);
      });

      expect(console.warn).toHaveBeenCalledWith('Failed to save cart to localStorage:', expect.any(Error));
    });
  });
});
