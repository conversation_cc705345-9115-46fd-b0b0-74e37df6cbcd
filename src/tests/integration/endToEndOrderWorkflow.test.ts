import mongoose, { Types } from 'mongoose';
import { NextRequest } from 'next/server';
import Order from '@/models/Order';
import DeliveryNote from '@/models/DeliveryNote';
import Location from '@/models/Location';
import BranchInventory from '@/models/BranchInventory';
import { Ingredient } from '@/models/Ingredient';
import UOM from '@/models/UOM';
import dbConnect from '@/lib/db';
import { AuthUser } from '@/lib/auth-helpers';

// Mock constants
const MOCK_COMPANY_ID = new mongoose.Types.ObjectId().toString();
const MOCK_BRANCH_LOCATION_ID = new mongoose.Types.ObjectId().toString();
const MOCK_CENTRAL_KITCHEN_ID = new mongoose.Types.ObjectId().toString();
const MOCK_USER_ID = new mongoose.Types.ObjectId().toString();

// Mock auth helpers
const mockRequireAuth: jest.Mock<Promise<AuthUser>> = jest.fn(async () => ({
  id: MOCK_USER_ID,
  email: '<EMAIL>',
  role: 'branch_manager',
  tenantId: MOCK_COMPANY_ID,
  metadata: {}
}));

const mockValidateIonicAuth = jest.fn();
const mockValidateLocationAccess = jest.fn();

jest.mock('@/lib/auth-helpers', () => ({
  requireAuth: mockRequireAuth,
  withAuth: (handler: any) => handler,
}));

jest.mock('@/lib/ionic-auth', () => ({
  validateIonicAuth: mockValidateIonicAuth
}));

jest.mock('@/lib/location-validation', () => ({
  validateLocationAccess: mockValidateLocationAccess
}));

describe('End-to-End Order Workflow Integration Tests', () => {
  const companyId = MOCK_COMPANY_ID;
  const branchLocationId = MOCK_BRANCH_LOCATION_ID;
  const centralKitchenId = MOCK_CENTRAL_KITCHEN_ID;
  let testIngredientId: string;
  let testUomId: string;
  let testBranchInventoryId: string;

  beforeAll(async () => {
    await dbConnect();

    // Create test UOM
    const uom = await UOM.create({
      companyId,
      name: 'Kilogram',
      shortCode: 'kg',
      system: 'metric',
      baseType: 'mass',
      factorToCanonical: 1
    });
    testUomId = uom._id.toString();

    // Create test ingredient
    const ingredient = await Ingredient.create({
      companyId,
      name: 'Premium Flour',
      description: 'High-quality flour for baking',
      category: 'Dry Goods',
      SKU: 'FLOUR-E2E-001',
      baseUomId: uom._id,
      isActive: true
    });
    testIngredientId = ingredient._id.toString();

    // Create branch location
    await Location.create({
      _id: new Types.ObjectId(branchLocationId),
      companyId,
      name: 'Downtown Branch',
      locationType: 'RETAIL_SHOP',
      canSellToExternal: false,
      canDoTransfers: true,
      canBuyfromExternalSuppliers: false,
      address: '123 Main St, Downtown',
      isActive: true
    });

    // Create central kitchen location
    await Location.create({
      _id: new Types.ObjectId(centralKitchenId),
      companyId,
      name: 'Main Central Kitchen',
      locationType: 'CENTRAL_KITCHEN',
      canSellToExternal: true,
      canDoTransfers: true,
      canBuyfromExternalSuppliers: true,
      address: '456 Industrial Ave',
      isActive: true
    });

    // Create branch inventory item at central kitchen
    const branchInventory = await BranchInventory.create({
      companyId,
      locationId: new Types.ObjectId(centralKitchenId),
      itemId: ingredient._id,
      itemType: 'INGREDIENT',
      baseUomId: uom._id,
      currentStock: 500,
      minOrderQuantity: 10,
      maxOrderQuantity: 100,
      costBasis: 8.75,
      isOrderable: true,
      isActive: true
    });
    testBranchInventoryId = branchInventory._id.toString();
  });

  afterAll(async () => {
    await mongoose.connection.dropDatabase();
    await mongoose.connection.close();
  });

  beforeEach(async () => {
    // Clean up orders and delivery notes before each test
    await Order.deleteMany({ companyId });
    await DeliveryNote.deleteMany({ companyId });
    
    // Reset mocks
    jest.clearAllMocks();
    mockRequireAuth.mockResolvedValue({
      id: MOCK_USER_ID,
      email: '<EMAIL>',
      role: 'branch_manager',
      tenantId: MOCK_COMPANY_ID,
      metadata: {}
    });
    mockValidateIonicAuth.mockResolvedValue({ isAuthenticated: true });
    mockValidateLocationAccess.mockResolvedValue(undefined);
  });

  describe('Complete Order Workflow: Branch → HQ → Delivery → Receipt', () => {
    it('should complete full order workflow successfully', async () => {
      // STEP 1: Branch places order through shop portal
      const orderData = {
        items: [{
          itemId: testIngredientId,
          branchInventoryId: testBranchInventoryId,
          quantity: 25,
          unitPrice: 8.75,
          notes: 'Urgent order for weekend rush'
        }],
        requestedDeliveryDate: new Date(Date.now() + 86400000).toISOString(), // Tomorrow
        notes: 'E2E test order - high priority',
        priority: 'URGENT' as const
      };

      const shopPortalUrl = `http://localhost/api/company/${companyId}/shop-portal/orders`;
      const shopPortalReq = new NextRequest(shopPortalUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-location-id': branchLocationId
        },
        body: JSON.stringify(orderData)
      });

      const { POST: shopPortalPOST } = await import('@/app/api/company/[companyId]/shop-portal/orders/route');
      const shopPortalResponse = await shopPortalPOST(shopPortalReq, { params: Promise.resolve({ companyId }) });
      const shopPortalResult = await shopPortalResponse.json();

      // Verify shop portal order creation
      expect(shopPortalResponse.status).toBe(201);
      expect(shopPortalResult.success).toBe(true);
      expect(shopPortalResult.data.orderNumber).toMatch(/^SH-\d{6}$/);
      expect(shopPortalResult.data.totalAmount).toBe(218.75); // 25 * 8.75

      const shopOrderId = shopPortalResult.data.orderId;

      // STEP 2: Verify order exists in HQ system
      const hqOrder = await Order.findById(shopOrderId);
      expect(hqOrder).toBeTruthy();
      expect(hqOrder?.status).toBe('CONFIRMED'); // Auto-approved
      expect(hqOrder?.buyer.buyerType).toBe('BRANCH');
      expect(hqOrder?.seller.sellerType).toBe('CENTRAL_KITCHEN');
      expect(hqOrder?.items).toHaveLength(1);
      expect(hqOrder?.items[0].quantity).toBe(25);

      // STEP 3: Simulate Ionic app sync (order already exists, so should be skipped)
      const ionicSyncData = {
        orders: [{
          orderNumber: hqOrder?.orderNumber || 'FALLBACK-001',
          items: [{
            itemId: testIngredientId,
            itemType: 'INGREDIENT',
            description: 'Premium Flour',
            quantity: 25,
            uomId: testUomId,
            unitPrice: 8.75,
            lineTotal: 218.75
          }],
          totalAmount: 218.75,
          status: 'CONFIRMED',
          orderTime: new Date().toISOString(),
          notes: 'Synced from shop portal'
        }]
      };

      const syncUrl = `http://localhost/api/company/${companyId}/location/${branchLocationId}/orders/sync`;
      const syncReq = new NextRequest(syncUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(ionicSyncData)
      });

      const { POST: syncPOST } = await import('@/app/api/company/[companyId]/location/[locationId]/orders/sync/route');
      const syncResponse = await syncPOST(syncReq, { 
        params: Promise.resolve({ companyId, locationId: branchLocationId }) 
      });
      const syncResult = await syncResponse.json();

      // Verify sync response (should skip duplicate)
      expect(syncResponse.status).toBe(200);
      expect(syncResult.success).toBe(true);
      expect(syncResult.skipped).toBe(1); // Order already exists

      // STEP 4: Verify delivery note was created
      const deliveryNote = await DeliveryNote.findOne({ 
        companyId,
        orderId: hqOrder?._id 
      });
      expect(deliveryNote).toBeTruthy();
      expect(deliveryNote?.status).toBe('PENDING_REVIEW');
      expect(deliveryNote?.source).toBe('WEB'); // Created from shop portal
      expect(deliveryNote?.handoverFlow).toHaveLength(3);

      // Verify handover flow structure
      const handoverFlow = deliveryNote?.handoverFlow;
      expect(handoverFlow?.[0].stepType).toBe('DISPATCH');
      expect(handoverFlow?.[0].status).toBe('PENDING');
      expect(handoverFlow?.[1].stepType).toBe('DRIVER');
      expect(handoverFlow?.[1].status).toBe('PENDING');
      expect(handoverFlow?.[2].stepType).toBe('SHOP');
      expect(handoverFlow?.[2].status).toBe('PENDING');

      // STEP 5: Simulate dispatch review and approval
      await DeliveryNote.findByIdAndUpdate(deliveryNote?._id, {
        status: 'FINALIZED',
        reviewed: true,
        reviewedBy: new Types.ObjectId(MOCK_USER_ID),
        reviewedAt: new Date()
      });

      // STEP 6: Simulate handover flow progression
      // Dispatch handover
      await DeliveryNote.findByIdAndUpdate(deliveryNote?._id, {
        'handoverFlow.0.status': 'SIGNED',
        'handoverFlow.0.signedAt': new Date(),
        'handoverFlow.0.signedBy': 'Dispatch Manager',
        'handoverFlow.0.confirmedItems': [{
          itemId: new Types.ObjectId(testIngredientId),
          confirmedQty: 25
        }],
        status: 'IN_PROGRESS'
      });

      // Driver handover
      await DeliveryNote.findByIdAndUpdate(deliveryNote?._id, {
        'handoverFlow.1.status': 'SIGNED',
        'handoverFlow.1.signedAt': new Date(),
        'handoverFlow.1.signedBy': 'Driver John',
        'handoverFlow.1.confirmedItems': [{
          itemId: new Types.ObjectId(testIngredientId),
          confirmedQty: 25
        }]
      });

      // STEP 7: Simulate shop receipt confirmation
      await DeliveryNote.findByIdAndUpdate(deliveryNote?._id, {
        'handoverFlow.2.status': 'SIGNED',
        'handoverFlow.2.signedAt': new Date(),
        'handoverFlow.2.signedBy': 'Shop Manager',
        'handoverFlow.2.confirmedItems': [{
          itemId: new Types.ObjectId(testIngredientId),
          confirmedQty: 25
        }],
        status: 'COMPLETED'
      });

      // STEP 8: Update order status to delivered
      await Order.findByIdAndUpdate(hqOrder?._id, {
        status: 'DELIVERED',
        'items.0.deliveredQuantity': 25,
        deliveredAt: new Date()
      });

      // STEP 9: Verify final state
      const finalOrder = await Order.findById(hqOrder?._id);
      const finalDeliveryNote = await DeliveryNote.findById(deliveryNote?._id);

      // Verify order completion
      expect(finalOrder?.status).toBe('DELIVERED');
      expect(finalOrder?.items[0].deliveredQuantity).toBe(25);
      expect(finalOrder?.deliveredAt).toBeDefined();

      // Verify delivery note completion
      expect(finalDeliveryNote?.status).toBe('COMPLETED');
      expect(finalDeliveryNote?.handoverFlow[0].status).toBe('SIGNED');
      expect(finalDeliveryNote?.handoverFlow[1].status).toBe('SIGNED');
      expect(finalDeliveryNote?.handoverFlow[2].status).toBe('SIGNED');

      // Verify all handover confirmations
      finalDeliveryNote?.handoverFlow.forEach(step => {
        expect(step.confirmedItems).toHaveLength(1);
        expect(step.confirmedItems[0].confirmedQty).toBe(25);
        expect(step.signedAt).toBeDefined();
        expect(step.signedBy).toBeDefined();
      });
    });

    it('should handle partial delivery and discrepancies', async () => {
      // Create order with larger quantity to test partial delivery
      const orderData = {
        items: [{
          itemId: testIngredientId,
          branchInventoryId: testBranchInventoryId,
          quantity: 50,
          unitPrice: 8.75
        }],
        notes: 'Large order for testing partial delivery'
      };

      const shopPortalUrl = `http://localhost/api/company/${companyId}/shop-portal/orders`;
      const shopPortalReq = new NextRequest(shopPortalUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-location-id': branchLocationId
        },
        body: JSON.stringify(orderData)
      });

      const { POST: shopPortalPOST } = await import('@/app/api/company/[companyId]/shop-portal/orders/route');
      const shopPortalResponse = await shopPortalPOST(shopPortalReq, { params: Promise.resolve({ companyId }) });
      const shopPortalResult = await shopPortalResponse.json();

      expect(shopPortalResponse.status).toBe(201);
      const shopOrderId = shopPortalResult.data.orderId;

      // Get created order and delivery note
      const order = await Order.findById(shopOrderId);
      const deliveryNote = await DeliveryNote.findOne({ orderId: order?._id });

      // Simulate partial delivery scenario
      // Dispatch confirms full quantity
      await DeliveryNote.findByIdAndUpdate(deliveryNote?._id, {
        'handoverFlow.0.status': 'SIGNED',
        'handoverFlow.0.confirmedItems': [{
          itemId: new Types.ObjectId(testIngredientId),
          confirmedQty: 50
        }],
        status: 'IN_PROGRESS'
      });

      // Driver reports shortage
      await DeliveryNote.findByIdAndUpdate(deliveryNote?._id, {
        'handoverFlow.1.status': 'SIGNED',
        'handoverFlow.1.confirmedItems': [{
          itemId: new Types.ObjectId(testIngredientId),
          confirmedQty: 40 // 10 bags short
        }],
        'handoverFlow.1.notes': '10 bags damaged during transport'
      });

      // Shop confirms partial receipt
      await DeliveryNote.findByIdAndUpdate(deliveryNote?._id, {
        'handoverFlow.2.status': 'SIGNED',
        'handoverFlow.2.confirmedItems': [{
          itemId: new Types.ObjectId(testIngredientId),
          confirmedQty: 40 // Confirms what driver delivered
        }],
        'handoverFlow.2.notes': 'Confirmed partial delivery - 10 bags missing',
        status: 'COMPLETED'
      });

      // Update order to reflect partial delivery
      await Order.findByIdAndUpdate(order?._id, {
        status: 'PARTIALLY_DELIVERED',
        'items.0.deliveredQuantity': 40
      });

      // Verify partial delivery state
      const finalOrder = await Order.findById(order?._id);
      const finalDeliveryNote = await DeliveryNote.findById(deliveryNote?._id);

      expect(finalOrder?.status).toBe('PARTIALLY_DELIVERED');
      expect(finalOrder?.items[0].quantity).toBe(50); // Original quantity
      expect(finalOrder?.items[0].deliveredQuantity).toBe(40); // Delivered quantity

      // Verify discrepancy tracking
      expect(finalDeliveryNote?.handoverFlow[1].confirmedItems[0].confirmedQty).toBe(40);
      expect(finalDeliveryNote?.handoverFlow[2].confirmedItems[0].confirmedQty).toBe(40);
      expect(finalDeliveryNote?.handoverFlow[1].notes).toContain('damaged during transport');
      expect(finalDeliveryNote?.handoverFlow[2].notes).toContain('partial delivery');
    });
  });

  describe('Cross-Platform Communication Tests', () => {
    it('should handle order status updates between platforms', async () => {
      // Create order through shop portal
      const orderData = {
        items: [{
          itemId: testIngredientId,
          branchInventoryId: testBranchInventoryId,
          quantity: 15,
          unitPrice: 8.75
        }],
        notes: 'Cross-platform test order'
      };

      const shopPortalUrl = `http://localhost/api/company/${companyId}/shop-portal/orders`;
      const shopPortalReq = new NextRequest(shopPortalUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-location-id': branchLocationId
        },
        body: JSON.stringify(orderData)
      });

      const { POST: shopPortalPOST } = await import('@/app/api/company/[companyId]/shop-portal/orders/route');
      const shopPortalResponse = await shopPortalPOST(shopPortalReq, { params: Promise.resolve({ companyId }) });
      const shopPortalResult = await shopPortalResponse.json();

      expect(shopPortalResponse.status).toBe(201);
      const orderId = shopPortalResult.data.orderId;

      // Verify order can be retrieved through shop portal GET endpoint
      const getOrderUrl = `http://localhost/api/company/${companyId}/shop-portal/orders?locationId=${branchLocationId}`;
      const getOrderReq = new NextRequest(getOrderUrl, {
        method: 'GET'
      });

      const { GET: shopPortalGET } = await import('@/app/api/company/[companyId]/shop-portal/orders/route');
      const getOrderResponse = await shopPortalGET(getOrderReq, { params: Promise.resolve({ companyId }) });
      const getOrderResult = await getOrderResponse.json();

      expect(getOrderResponse.status).toBe(200);
      expect(getOrderResult.success).toBe(true);
      expect(getOrderResult.data).toHaveLength(1);
      expect(getOrderResult.data[0]._id).toBe(orderId);

      // Simulate status update from HQ system
      const order = await Order.findById(orderId);
      await Order.findByIdAndUpdate(orderId, {
        status: 'APPROVED',
        approvedBy: new Types.ObjectId(MOCK_USER_ID),
        approvedAt: new Date()
      });

      // Verify updated status is reflected in shop portal
      const getUpdatedOrderResponse = await shopPortalGET(getOrderReq, { params: Promise.resolve({ companyId }) });
      const getUpdatedOrderResult = await getUpdatedOrderResponse.json();

      expect(getUpdatedOrderResult.data[0].status).toBe('APPROVED');
      expect(getUpdatedOrderResult.data[0].approvedAt).toBeDefined();
    });

    it('should maintain data consistency across sync operations', async () => {
      // Create multiple orders through different entry points
      const shopPortalOrder = {
        items: [{
          itemId: testIngredientId,
          branchInventoryId: testBranchInventoryId,
          quantity: 20,
          unitPrice: 8.75
        }],
        notes: 'Shop portal order'
      };

      const ionicOrder = {
        orders: [{
          orderNumber: 'IONIC-CONSISTENCY-001',
          items: [{
            itemId: testIngredientId,
            itemType: 'INGREDIENT',
            description: 'Premium Flour',
            quantity: 30,
            uomId: testUomId,
            unitPrice: 8.75,
            lineTotal: 262.50
          }],
          totalAmount: 262.50,
          status: 'CONFIRMED',
          orderTime: new Date().toISOString(),
          notes: 'Ionic app order'
        }]
      };

      // Create shop portal order
      const shopPortalUrl = `http://localhost/api/company/${companyId}/shop-portal/orders`;
      const shopPortalReq = new NextRequest(shopPortalUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-location-id': branchLocationId
        },
        body: JSON.stringify(shopPortalOrder)
      });

      const { POST: shopPortalPOST } = await import('@/app/api/company/[companyId]/shop-portal/orders/route');
      await shopPortalPOST(shopPortalReq, { params: Promise.resolve({ companyId }) });

      // Sync ionic order
      const syncUrl = `http://localhost/api/company/${companyId}/location/${branchLocationId}/orders/sync`;
      const syncReq = new NextRequest(syncUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(ionicOrder)
      });

      const { POST: syncPOST } = await import('@/app/api/company/[companyId]/location/[locationId]/orders/sync/route');
      await syncPOST(syncReq, {
        params: Promise.resolve({ companyId, locationId: branchLocationId })
      });

      // Verify both orders exist with correct metadata
      const allOrders = await Order.find({ companyId }).sort({ createdAt: 1 });
      expect(allOrders).toHaveLength(2);

      const shopOrder = allOrders.find(o => o.orderSource !== 'IONIC');
      const ionicSyncedOrder = allOrders.find(o => o.orderSource === 'IONIC');

      expect(shopOrder?.orderSource).toBeUndefined(); // Default for shop portal
      expect(shopOrder?.syncStatus).toBeUndefined();
      expect(shopOrder?.items[0].quantity).toBe(20);

      expect(ionicSyncedOrder?.orderSource).toBe('IONIC');
      expect(ionicSyncedOrder?.syncStatus).toBe('SYNCED');
      expect(ionicSyncedOrder?.originalOrderNumber).toBe('IONIC-CONSISTENCY-001');
      expect(ionicSyncedOrder?.items[0].quantity).toBe(30);

      // Verify delivery notes were created for both
      const deliveryNotes = await DeliveryNote.find({ companyId });
      expect(deliveryNotes).toHaveLength(2);

      const shopDeliveryNote = deliveryNotes.find(dn => dn.source === 'WEB');
      const ionicDeliveryNote = deliveryNotes.find(dn => dn.source === 'IONIC');

      expect(shopDeliveryNote).toBeTruthy();
      expect(ionicDeliveryNote).toBeTruthy();
      expect(shopDeliveryNote?.orderId.toString()).toBe(shopOrder?._id.toString());
      expect(ionicDeliveryNote?.orderId.toString()).toBe(ionicSyncedOrder?._id.toString());
    });

    it('should handle concurrent order processing', async () => {
      // Simulate concurrent order creation from multiple sources
      const concurrentOrders = [
        {
          type: 'shop-portal',
          data: {
            items: [{
              itemId: testIngredientId,
              branchInventoryId: testBranchInventoryId,
              quantity: 10,
              unitPrice: 8.75
            }],
            notes: 'Concurrent order 1'
          }
        },
        {
          type: 'ionic-sync',
          data: {
            orders: [{
              orderNumber: 'IONIC-CONCURRENT-001',
              items: [{
                itemId: testIngredientId,
                itemType: 'INGREDIENT',
                description: 'Premium Flour',
                quantity: 12,
                uomId: testUomId,
                unitPrice: 8.75,
                lineTotal: 105
              }],
              totalAmount: 105,
              status: 'CONFIRMED',
              orderTime: new Date().toISOString(),
              notes: 'Concurrent order 2'
            }]
          }
        }
      ];

      // Process orders concurrently
      const promises = concurrentOrders.map(async (orderConfig) => {
        if (orderConfig.type === 'shop-portal') {
          const url = `http://localhost/api/company/${companyId}/shop-portal/orders`;
          const req = new NextRequest(url, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'x-location-id': branchLocationId
            },
            body: JSON.stringify(orderConfig.data)
          });

          const { POST } = await import('@/app/api/company/[companyId]/shop-portal/orders/route');
          return POST(req, { params: Promise.resolve({ companyId }) });
        } else {
          const url = `http://localhost/api/company/${companyId}/location/${branchLocationId}/orders/sync`;
          const req = new NextRequest(url, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(orderConfig.data)
          });

          const { POST } = await import('@/app/api/company/[companyId]/location/[locationId]/orders/sync/route');
          return POST(req, {
            params: Promise.resolve({ companyId, locationId: branchLocationId })
          });
        }
      });

      const responses = await Promise.all(promises);

      // Verify all requests succeeded
      responses.forEach(response => {
        expect(response.status).toBeOneOf([200, 201]);
      });

      // Verify orders were created correctly
      const orders = await Order.find({ companyId });
      expect(orders).toHaveLength(2);

      // Verify delivery notes were created
      const deliveryNotes = await DeliveryNote.find({ companyId });
      expect(deliveryNotes).toHaveLength(2);

      // Verify no data corruption occurred
      orders.forEach(order => {
        expect(order.companyId).toBe(companyId);
        expect(order.items).toHaveLength(1);
        expect(order.items[0].itemId.toString()).toBe(testIngredientId);
        expect(order.totalAmount).toBeGreaterThan(0);
      });
    });
  });
});
