import mongoose, { Types } from 'mongoose';
import { NextRequest } from 'next/server';
import Order from '@/models/Order';
import DeliveryNote from '@/models/DeliveryNote';
import Location from '@/models/Location';
import { Ingredient } from '@/models/Ingredient';
import UOM from '@/models/UOM';
import dbConnect from '@/lib/db';

// Mock constants
const MOCK_COMPANY_ID = new mongoose.Types.ObjectId().toString();
const MOCK_BRANCH_LOCATION_ID = new mongoose.Types.ObjectId().toString();
const MOCK_CENTRAL_KITCHEN_ID = new mongoose.Types.ObjectId().toString();

// Mock auth helpers
const mockValidateIonicAuth = jest.fn();
const mockValidateLocationAccess = jest.fn();

jest.mock('@/lib/ionic-auth', () => ({
  validateIonicAuth: mockValidateIonicAuth
}));

jest.mock('@/lib/location-validation', () => ({
  validateLocationAccess: mockValidateLocationAccess
}));

describe('Order Sync Integration Tests', () => {
  const companyId = MOCK_COMPANY_ID;
  const branchLocationId = MOCK_BRANCH_LOCATION_ID;
  const centralKitchenId = MOCK_CENTRAL_KITCHEN_ID;
  let testIngredientId: string;
  let testUomId: string;

  beforeAll(async () => {
    await dbConnect();

    // Create test UOM
    const uom = await UOM.create({
      companyId,
      name: 'Kilogram',
      shortCode: 'kg',
      system: 'metric',
      baseType: 'mass',
      factorToCanonical: 1
    });
    testUomId = uom._id.toString();

    // Create test ingredient
    const ingredient = await Ingredient.create({
      companyId,
      name: 'Test Flour',
      description: 'Test ingredient for sync tests',
      category: 'Dry Goods',
      SKU: 'TEST-FLOUR-SYNC-001',
      baseUomId: uom._id,
      isActive: true
    });
    testIngredientId = ingredient._id.toString();

    // Create branch location
    await Location.create({
      _id: new Types.ObjectId(branchLocationId),
      companyId,
      name: 'Test Branch Location',
      locationType: 'RETAIL_SHOP',
      canSellToExternal: false,
      canDoTransfers: true,
      canBuyfromExternalSuppliers: false,
      address: '123 Branch St',
      isActive: true
    });

    // Create central kitchen location
    await Location.create({
      _id: new Types.ObjectId(centralKitchenId),
      companyId,
      name: 'Test Central Kitchen',
      locationType: 'CENTRAL_KITCHEN',
      canSellToExternal: true,
      canDoTransfers: true,
      canBuyfromExternalSuppliers: true,
      address: '456 Kitchen Ave',
      isActive: true
    });
  });

  afterAll(async () => {
    await mongoose.connection.dropDatabase();
    await mongoose.connection.close();
  });

  beforeEach(async () => {
    // Clean up orders and delivery notes before each test
    await Order.deleteMany({ companyId });
    await DeliveryNote.deleteMany({ companyId });
    
    // Reset mocks
    jest.clearAllMocks();
    mockValidateIonicAuth.mockResolvedValue({ isAuthenticated: true });
    mockValidateLocationAccess.mockResolvedValue(undefined);
  });

  describe('Successful Order Sync Scenarios', () => {
    it('should sync single order from Ionic app successfully', async () => {
      const orderData = {
        syncId: 'sync-test-001',
        syncTimestamp: new Date().toISOString(),
        orders: [{
          orderNumber: 'IONIC-SYNC-001',
          items: [{
            itemId: testIngredientId,
            itemType: 'INGREDIENT',
            description: 'Test Flour',
            quantity: 15,
            uomId: testUomId,
            unitPrice: 6.75,
            lineTotal: 101.25
          }],
          totalAmount: 101.25,
          status: 'CONFIRMED',
          orderTime: new Date().toISOString(),
          notes: 'Sync test order',
          customerInfo: {
            name: 'Test Customer',
            phone: '************'
          }
        }]
      };

      const url = `http://localhost/api/company/${companyId}/location/${branchLocationId}/orders/sync`;
      const req = new NextRequest(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-ionic-token'
        },
        body: JSON.stringify(orderData)
      });

      const { POST } = await import('@/app/api/company/[companyId]/location/[locationId]/orders/sync/route');
      const response = await POST(req, { 
        params: Promise.resolve({ companyId, locationId: branchLocationId }) 
      });
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.processed).toBe(1);
      expect(result.failed).toBe(0);

      // Verify order was created with correct sync metadata
      const syncedOrder = await Order.findOne({ 
        companyId, 
        originalOrderNumber: 'IONIC-SYNC-001' 
      });
      expect(syncedOrder).toBeTruthy();
      expect(syncedOrder?.orderSource).toBe('IONIC');
      expect(syncedOrder?.syncStatus).toBe('SYNCED');
      expect(syncedOrder?.status).toBe('CONFIRMED');
      expect(syncedOrder?.totalAmount).toBe(101.25);

      // Verify delivery note was created
      const deliveryNote = await DeliveryNote.findOne({ 
        companyId,
        orderId: syncedOrder?._id 
      });
      expect(deliveryNote).toBeTruthy();
      expect(deliveryNote?.source).toBe('IONIC');
    });

    it('should handle batch sync with multiple orders', async () => {
      const orderData = {
        syncId: 'sync-batch-001',
        syncTimestamp: new Date().toISOString(),
        orders: [
          {
            orderNumber: 'IONIC-BATCH-001',
            items: [{
              itemId: testIngredientId,
              itemType: 'INGREDIENT',
              description: 'Test Flour',
              quantity: 10,
              uomId: testUomId,
              unitPrice: 5.00,
              lineTotal: 50.00
            }],
            totalAmount: 50.00,
            status: 'CONFIRMED',
            orderTime: new Date(Date.now() - 3600000).toISOString() // 1 hour ago
          },
          {
            orderNumber: 'IONIC-BATCH-002',
            items: [{
              itemId: testIngredientId,
              itemType: 'INGREDIENT',
              description: 'Test Flour',
              quantity: 20,
              uomId: testUomId,
              unitPrice: 5.00,
              lineTotal: 100.00
            }],
            totalAmount: 100.00,
            status: 'CONFIRMED',
            orderTime: new Date().toISOString()
          },
          {
            orderNumber: 'IONIC-BATCH-003',
            items: [{
              itemId: testIngredientId,
              itemType: 'INGREDIENT',
              description: 'Test Flour',
              quantity: 5,
              uomId: testUomId,
              unitPrice: 5.00,
              lineTotal: 25.00
            }],
            totalAmount: 25.00,
            status: 'DRAFT',
            orderTime: new Date().toISOString()
          }
        ]
      };

      const url = `http://localhost/api/company/${companyId}/location/${branchLocationId}/orders/sync`;
      const req = new NextRequest(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-ionic-token'
        },
        body: JSON.stringify(orderData)
      });

      const { POST } = await import('@/app/api/company/[companyId]/location/[locationId]/orders/sync/route');
      const response = await POST(req, { 
        params: Promise.resolve({ companyId, locationId: branchLocationId }) 
      });
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.processed).toBe(3);
      expect(result.failed).toBe(0);

      // Verify all orders were created
      const syncedOrders = await Order.find({ companyId }).sort({ createdAt: 1 });
      expect(syncedOrders).toHaveLength(3);
      
      // Verify order details
      expect(syncedOrders[0].originalOrderNumber).toBe('IONIC-BATCH-001');
      expect(syncedOrders[1].originalOrderNumber).toBe('IONIC-BATCH-002');
      expect(syncedOrders[2].originalOrderNumber).toBe('IONIC-BATCH-003');
      
      // Verify different statuses
      expect(syncedOrders[0].status).toBe('CONFIRMED');
      expect(syncedOrders[1].status).toBe('CONFIRMED');
      expect(syncedOrders[2].status).toBe('DRAFT');

      // Verify delivery notes created for confirmed orders only
      const deliveryNotes = await DeliveryNote.find({ companyId });
      expect(deliveryNotes).toHaveLength(2); // Only for CONFIRMED orders
    });

    it('should prevent duplicate order sync', async () => {
      // First sync
      const orderData = {
        orders: [{
          orderNumber: 'IONIC-DUP-001',
          items: [{
            itemId: testIngredientId,
            itemType: 'INGREDIENT',
            description: 'Test Flour',
            quantity: 10,
            uomId: testUomId,
            unitPrice: 5.00,
            lineTotal: 50.00
          }],
          totalAmount: 50.00,
          status: 'CONFIRMED',
          orderTime: new Date().toISOString()
        }]
      };

      const url = `http://localhost/api/company/${companyId}/location/${branchLocationId}/orders/sync`;
      
      // First sync request
      let req = new NextRequest(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-ionic-token'
        },
        body: JSON.stringify(orderData)
      });

      const { POST } = await import('@/app/api/company/[companyId]/location/[locationId]/orders/sync/route');
      let response = await POST(req, { 
        params: Promise.resolve({ companyId, locationId: branchLocationId }) 
      });
      let result = await response.json();

      expect(response.status).toBe(200);
      expect(result.processed).toBe(1);

      // Second sync request with same order
      req = new NextRequest(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-ionic-token'
        },
        body: JSON.stringify(orderData)
      });

      response = await POST(req, { 
        params: Promise.resolve({ companyId, locationId: branchLocationId }) 
      });
      result = await response.json();

      expect(response.status).toBe(200);
      expect(result.processed).toBe(0); // Should skip duplicate
      expect(result.skipped).toBe(1);

      // Verify only one order exists
      const orders = await Order.find({ companyId });
      expect(orders).toHaveLength(1);
    });
  });

  describe('Error Handling and Authentication', () => {
    it('should reject requests with invalid authentication', async () => {
      mockValidateIonicAuth.mockResolvedValue({ isAuthenticated: false });

      const orderData = {
        orders: [{
          orderNumber: 'IONIC-AUTH-001',
          items: [{
            itemId: testIngredientId,
            itemType: 'INGREDIENT',
            description: 'Test Flour',
            quantity: 10,
            uomId: testUomId,
            unitPrice: 5.00,
            lineTotal: 50.00
          }],
          totalAmount: 50.00,
          status: 'CONFIRMED',
          orderTime: new Date().toISOString()
        }]
      };

      const url = `http://localhost/api/company/${companyId}/location/${branchLocationId}/orders/sync`;
      const req = new NextRequest(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer invalid-token'
        },
        body: JSON.stringify(orderData)
      });

      const { POST } = await import('@/app/api/company/[companyId]/location/[locationId]/orders/sync/route');
      const response = await POST(req, {
        params: Promise.resolve({ companyId, locationId: branchLocationId })
      });

      expect(response.status).toBe(401);

      // Verify no orders were created
      const orders = await Order.find({ companyId });
      expect(orders).toHaveLength(0);
    });

    it('should reject requests with invalid location access', async () => {
      mockValidateLocationAccess.mockRejectedValue(new Error('Location access denied'));

      const orderData = {
        orders: [{
          orderNumber: 'IONIC-LOC-001',
          items: [{
            itemId: testIngredientId,
            itemType: 'INGREDIENT',
            description: 'Test Flour',
            quantity: 10,
            uomId: testUomId,
            unitPrice: 5.00,
            lineTotal: 50.00
          }],
          totalAmount: 50.00,
          status: 'CONFIRMED',
          orderTime: new Date().toISOString()
        }]
      };

      const url = `http://localhost/api/company/${companyId}/location/${branchLocationId}/orders/sync`;
      const req = new NextRequest(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-ionic-token'
        },
        body: JSON.stringify(orderData)
      });

      const { POST } = await import('@/app/api/company/[companyId]/location/[locationId]/orders/sync/route');
      const response = await POST(req, {
        params: Promise.resolve({ companyId, locationId: branchLocationId })
      });

      expect(response.status).toBe(403);

      // Verify no orders were created
      const orders = await Order.find({ companyId });
      expect(orders).toHaveLength(0);
    });

    it('should handle malformed request data gracefully', async () => {
      const malformedData = {
        orders: [{
          // Missing required fields
          orderNumber: 'IONIC-MAL-001',
          items: [{
            // Missing itemId
            itemType: 'INGREDIENT',
            description: 'Test Flour',
            quantity: 10,
            unitPrice: 5.00,
            lineTotal: 50.00
          }],
          totalAmount: 50.00,
          status: 'CONFIRMED'
          // Missing orderTime
        }]
      };

      const url = `http://localhost/api/company/${companyId}/location/${branchLocationId}/orders/sync`;
      const req = new NextRequest(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-ionic-token'
        },
        body: JSON.stringify(malformedData)
      });

      const { POST } = await import('@/app/api/company/[companyId]/location/[locationId]/orders/sync/route');
      const response = await POST(req, {
        params: Promise.resolve({ companyId, locationId: branchLocationId })
      });
      const result = await response.json();

      expect(response.status).toBe(200); // Should still return 200 but with errors
      expect(result.success).toBe(true);
      expect(result.processed).toBe(0);
      expect(result.failed).toBe(1);
      expect(result.errors).toHaveLength(1);
    });

    it('should handle network timeout and retry scenarios', async () => {
      // Simulate a scenario where some orders succeed and others fail
      const mixedOrderData = {
        orders: [
          {
            orderNumber: 'IONIC-MIX-001',
            items: [{
              itemId: testIngredientId,
              itemType: 'INGREDIENT',
              description: 'Test Flour',
              quantity: 10,
              uomId: testUomId,
              unitPrice: 5.00,
              lineTotal: 50.00
            }],
            totalAmount: 50.00,
            status: 'CONFIRMED',
            orderTime: new Date().toISOString()
          },
          {
            orderNumber: 'IONIC-MIX-002',
            items: [{
              itemId: 'invalid-item-id', // This will cause an error
              itemType: 'INGREDIENT',
              description: 'Invalid Item',
              quantity: 5,
              uomId: testUomId,
              unitPrice: 3.00,
              lineTotal: 15.00
            }],
            totalAmount: 15.00,
            status: 'CONFIRMED',
            orderTime: new Date().toISOString()
          }
        ]
      };

      const url = `http://localhost/api/company/${companyId}/location/${branchLocationId}/orders/sync`;
      const req = new NextRequest(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-ionic-token'
        },
        body: JSON.stringify(mixedOrderData)
      });

      const { POST } = await import('@/app/api/company/[companyId]/location/[locationId]/orders/sync/route');
      const response = await POST(req, {
        params: Promise.resolve({ companyId, locationId: branchLocationId })
      });
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.processed).toBe(1); // One successful
      expect(result.failed).toBe(1); // One failed

      // Verify only the valid order was created
      const orders = await Order.find({ companyId });
      expect(orders).toHaveLength(1);
      expect(orders[0].originalOrderNumber).toBe('IONIC-MIX-001');
    });
  });
});
