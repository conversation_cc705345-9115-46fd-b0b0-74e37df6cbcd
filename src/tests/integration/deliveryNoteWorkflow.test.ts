import mongoose, { Types } from 'mongoose';
import { NextRequest } from 'next/server';
import Order from '@/models/Order';
import DeliveryNote from '@/models/DeliveryNote';
import Location from '@/models/Location';
import BranchInventory from '@/models/BranchInventory';
import { Ingredient } from '@/models/Ingredient';
import UOM from '@/models/UOM';
import dbConnect from '@/lib/db';

// Mock constants
const MOCK_COMPANY_ID = new mongoose.Types.ObjectId().toString();
const MOCK_BRANCH_LOCATION_ID = new mongoose.Types.ObjectId().toString();
const MOCK_CENTRAL_KITCHEN_ID = new mongoose.Types.ObjectId().toString();

// Mock auth helpers
jest.mock('@/lib/ionic-auth', () => ({
  validateIonicAuth: jest.fn().mockResolvedValue({ isAuthenticated: true })
}));

jest.mock('@/lib/location-validation', () => ({
  validateLocationAccess: jest.fn().mockResolvedValue(undefined)
}));

describe('Delivery Note Workflow Tests', () => {
  const companyId = MOCK_COMPANY_ID;
  const branchLocationId = MOCK_BRANCH_LOCATION_ID;
  const centralKitchenId = MOCK_CENTRAL_KITCHEN_ID;
  let testIngredientId: string;
  let testUomId: string;

  beforeAll(async () => {
    await dbConnect();

    // Create test UOM
    const uom = await UOM.create({
      companyId,
      name: 'Kilogram',
      shortCode: 'kg',
      system: 'metric',
      baseType: 'mass',
      factorToCanonical: 1
    });
    testUomId = uom._id.toString();

    // Create test ingredient
    const ingredient = await Ingredient.create({
      companyId,
      name: 'Test Flour',
      description: 'Test ingredient for delivery notes',
      category: 'Dry Goods',
      SKU: 'TEST-FLOUR-DN-001',
      baseUomId: uom._id,
      isActive: true
    });
    testIngredientId = ingredient._id.toString();

    // Create branch location
    await Location.create({
      _id: new Types.ObjectId(branchLocationId),
      companyId,
      name: 'Test Branch Location',
      locationType: 'RETAIL_SHOP',
      canSellToExternal: false,
      canDoTransfers: true,
      canBuyfromExternalSuppliers: false,
      address: '123 Branch St',
      isActive: true
    });

    // Create central kitchen location
    await Location.create({
      _id: new Types.ObjectId(centralKitchenId),
      companyId,
      name: 'Test Central Kitchen',
      locationType: 'CENTRAL_KITCHEN',
      canSellToExternal: true,
      canDoTransfers: true,
      canBuyfromExternalSuppliers: true,
      address: '456 Kitchen Ave',
      isActive: true
    });

    // Create branch inventory item at branch location (for sync validation)
    await BranchInventory.create({
      companyId,
      locationId: new Types.ObjectId(branchLocationId),
      itemId: ingredient._id,
      itemType: 'INGREDIENT',
      baseUomId: uom._id,
      currentStock: 50,
      centralKitchenStock: 500, // This is what the validation function checks
      minOrderQuantity: 1,
      maxOrderQuantity: 100,
      costBasis: 5.50,
      isOrderable: true,
      isActive: true
    });

    // Create branch inventory item at central kitchen (for stock validation)
    await BranchInventory.create({
      companyId,
      locationId: new Types.ObjectId(centralKitchenId),
      itemId: ingredient._id,
      itemType: 'INGREDIENT',
      baseUomId: uom._id,
      currentStock: 500,
      centralKitchenStock: 500, // This is what the validation function checks
      minOrderQuantity: 1,
      maxOrderQuantity: 100,
      costBasis: 5.50,
      isOrderable: true,
      isActive: true
    });
  });

  afterAll(async () => {
    await mongoose.connection.dropDatabase();
    await mongoose.connection.close();
  });

  beforeEach(async () => {
    // Clean up orders and delivery notes before each test
    await Order.deleteMany({ companyId });
    await DeliveryNote.deleteMany({ companyId });
  });

  describe('Order Sync and Delivery Note Creation', () => {
    it('should create delivery note when order is synced from Ionic app', async () => {
      const orderData = {
        syncId: 'test-sync-001',
        syncTimestamp: new Date().toISOString(),
        orders: [{
          orderId: 'ionic-order-001',
          orderNumber: 'ION-001',
          items: [{
            itemId: testIngredientId,
            itemType: 'INGREDIENT',
            description: 'Test Flour',
            quantity: 10,
            uomId: testUomId,
            unitPrice: 5.50,
            lineTotal: 55.00
          }],
          totalAmount: 55.00,
          status: 'CONFIRMED',
          orderTime: new Date().toISOString(),
          notes: 'Test order from Ionic'
        }]
      };

      const url = `http://localhost/api/company/${companyId}/location/${branchLocationId}/orders/sync`;
      const req = new NextRequest(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(orderData)
      });

      const { POST } = await import('@/app/api/company/[companyId]/location/[locationId]/orders/sync/route');
      const response = await POST(req, {
        params: Promise.resolve({ companyId, locationId: branchLocationId })
      });
      const result = await response.json();

      if (response.status !== 200) {
        console.log('Error response:', result);
      }

      expect(response.status).toBe(200);
      expect(result.status).toBe('COMPLETED');
      expect(result.processedOrders).toHaveLength(1);

      // Verify order was created
      const createdOrder = await Order.findOne({ 
        companyId, 
        originalOrderNumber: 'ION-001' 
      });
      expect(createdOrder).toBeTruthy();
      expect(createdOrder?.status).toBe('CONFIRMED');
      expect(createdOrder?.orderSource).toBe('IONIC');

      // Verify delivery note was created
      const deliveryNote = await DeliveryNote.findOne({ 
        companyId,
        orderId: createdOrder?._id 
      });
      expect(deliveryNote).toBeTruthy();
      expect(deliveryNote?.status).toBe('PENDING_REVIEW');
      expect(deliveryNote?.source).toBe('IONIC');
      expect(deliveryNote?.reviewed).toBe(false);
      expect(deliveryNote?.handoverFlow).toHaveLength(3);
      
      // Verify handover flow structure
      const handoverFlow = deliveryNote?.handoverFlow;
      expect(handoverFlow?.[0].stepType).toBe('DISPATCH');
      expect(handoverFlow?.[0].status).toBe('PENDING');
      expect(handoverFlow?.[1].stepType).toBe('DRIVER');
      expect(handoverFlow?.[1].status).toBe('PENDING');
      expect(handoverFlow?.[2].stepType).toBe('SHOP');
      expect(handoverFlow?.[2].status).toBe('PENDING');
    });

    it('should handle multiple orders in single sync request', async () => {
      const orderData = {
        syncId: 'test-sync-002',
        syncTimestamp: new Date().toISOString(),
        orders: [
          {
            orderNumber: 'ION-002',
            items: [{
              itemId: testIngredientId,
              itemType: 'INGREDIENT',
              description: 'Test Flour',
              quantity: 5,
              uomId: testUomId,
              unitPrice: 5.50,
              lineTotal: 27.50
            }],
            totalAmount: 27.50,
            status: 'CONFIRMED',
            orderTime: new Date().toISOString()
          },
          {
            orderNumber: 'ION-003',
            items: [{
              itemId: testIngredientId,
              itemType: 'INGREDIENT',
              description: 'Test Flour',
              quantity: 8,
              uomId: testUomId,
              unitPrice: 5.50,
              lineTotal: 44.00
            }],
            totalAmount: 44.00,
            status: 'CONFIRMED',
            orderTime: new Date().toISOString()
          }
        ]
      };

      const url = `http://localhost/api/company/${companyId}/location/${branchLocationId}/orders/sync`;
      const req = new NextRequest(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(orderData)
      });

      const { POST } = await import('@/app/api/company/[companyId]/location/[locationId]/orders/sync/route');
      const response = await POST(req, { 
        params: Promise.resolve({ companyId, locationId: branchLocationId }) 
      });
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.processed).toBe(2);

      // Verify both orders were created
      const orders = await Order.find({ companyId }).sort({ createdAt: 1 });
      expect(orders).toHaveLength(2);
      expect(orders[0].originalOrderNumber).toBe('ION-002');
      expect(orders[1].originalOrderNumber).toBe('ION-003');

      // Verify both delivery notes were created
      const deliveryNotes = await DeliveryNote.find({ companyId }).sort({ createdAt: 1 });
      expect(deliveryNotes).toHaveLength(2);
      expect(deliveryNotes[0].orderId.toString()).toBe(orders[0]._id.toString());
      expect(deliveryNotes[1].orderId.toString()).toBe(orders[1]._id.toString());
    });

    it('should generate unique delivery note numbers', async () => {
      const orderData = {
        syncId: 'test-sync-003',
        syncTimestamp: new Date().toISOString(),
        orders: [
          {
            orderNumber: 'ION-004',
            items: [{
              itemId: testIngredientId,
              itemType: 'INGREDIENT',
              description: 'Test Flour',
              quantity: 3,
              uomId: testUomId,
              unitPrice: 5.50,
              lineTotal: 16.50
            }],
            totalAmount: 16.50,
            status: 'CONFIRMED',
            orderTime: new Date().toISOString()
          },
          {
            orderNumber: 'ION-005',
            items: [{
              itemId: testIngredientId,
              itemType: 'INGREDIENT',
              description: 'Test Flour',
              quantity: 7,
              uomId: testUomId,
              unitPrice: 5.50,
              lineTotal: 38.50
            }],
            totalAmount: 38.50,
            status: 'CONFIRMED',
            orderTime: new Date().toISOString()
          }
        ]
      };

      const url = `http://localhost/api/company/${companyId}/location/${branchLocationId}/orders/sync`;
      const req = new NextRequest(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(orderData)
      });

      const { POST } = await import('@/app/api/company/[companyId]/location/[locationId]/orders/sync/route');
      await POST(req, { 
        params: Promise.resolve({ companyId, locationId: branchLocationId }) 
      });

      // Verify delivery notes have unique numbers
      const deliveryNotes = await DeliveryNote.find({ companyId }).sort({ createdAt: 1 });
      expect(deliveryNotes).toHaveLength(2);
      expect(deliveryNotes[0].deliveryNoteNumber).not.toBe(deliveryNotes[1].deliveryNoteNumber);
      expect(deliveryNotes[0].deliveryNoteNumber).toMatch(/^DN-\d{6}$/);
      expect(deliveryNotes[1].deliveryNoteNumber).toMatch(/^DN-\d{6}$/);
    });

    it('should handle orders with invalid items gracefully', async () => {
      const orderData = {
        syncId: 'test-sync-004',
        syncTimestamp: new Date().toISOString(),
        orders: [{
          orderNumber: 'ION-006',
          items: [
            {
              itemId: testIngredientId,
              itemType: 'INGREDIENT',
              description: 'Valid Test Flour',
              quantity: 5,
              uomId: testUomId,
              unitPrice: 5.50,
              lineTotal: 27.50
            },
            {
              itemId: null, // Invalid item
              itemType: 'INGREDIENT',
              description: 'Invalid Item',
              quantity: 3,
              uomId: testUomId,
              unitPrice: 2.00,
              lineTotal: 6.00
            }
          ],
          totalAmount: 33.50,
          status: 'CONFIRMED',
          orderTime: new Date().toISOString()
        }]
      };

      const url = `http://localhost/api/company/${companyId}/location/${branchLocationId}/orders/sync`;
      const req = new NextRequest(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(orderData)
      });

      const { POST } = await import('@/app/api/company/[companyId]/location/[locationId]/orders/sync/route');
      const response = await POST(req, {
        params: Promise.resolve({ companyId, locationId: branchLocationId })
      });
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.processed).toBe(1);

      // Verify order was created with only valid items
      const createdOrder = await Order.findOne({
        companyId,
        originalOrderNumber: 'ION-006'
      });
      expect(createdOrder).toBeTruthy();
      expect(createdOrder?.items).toHaveLength(1); // Only valid item

      // Verify delivery note was still created
      const deliveryNote = await DeliveryNote.findOne({
        companyId,
        orderId: createdOrder?._id
      });
      expect(deliveryNote).toBeTruthy();
      expect(deliveryNote?.items).toHaveLength(1); // Only valid item
    });
  });

  describe('Delivery Note Status Transitions', () => {
    let testOrderId: string;
    let testDeliveryNoteId: string;

    beforeEach(async () => {
      // Create a test order and delivery note for status transition tests
      const order = await Order.create({
        companyId,
        orderNumber: 'TEST-ORDER-001',
        originalOrderNumber: 'ION-STATUS-001',
        status: 'CONFIRMED',
        buyer: {
          buyerType: 'BRANCH',
          buyerId: branchLocationId,
          buyerName: 'Test Branch Location'
        },
        seller: {
          sellerType: 'CENTRAL_KITCHEN',
          sellerId: centralKitchenId,
          sellerName: 'Test Central Kitchen'
        },
        sellerLocationId: new Types.ObjectId(centralKitchenId),
        buyerLocationId: new Types.ObjectId(branchLocationId),
        items: [{
          itemType: 'INGREDIENT',
          itemId: new Types.ObjectId(testIngredientId),
          description: 'Test Flour',
          quantity: 10,
          deliveredQuantity: 0,
          uomId: new Types.ObjectId(testUomId),
          unitPrice: 5.50,
          lineTotal: 55.00
        }],
        orderSource: 'IONIC',
        totalAmount: 55.00
      });
      testOrderId = order._id.toString();

      const deliveryNote = await DeliveryNote.create({
        companyId,
        deliveryNoteNumber: 'DN-TEST-001',
        status: 'PENDING_REVIEW',
        source: 'IONIC',
        reviewed: false,
        orderId: order._id,
        deliveryDate: new Date(),
        items: [{
          itemId: new Types.ObjectId(testIngredientId),
          quantityPlanned: 10,
          uomId: new Types.ObjectId(testUomId)
        }],
        handoverFlow: [
          {
            stepType: 'DISPATCH',
            status: 'PENDING',
            confirmedItems: []
          },
          {
            stepType: 'DRIVER',
            status: 'PENDING',
            confirmedItems: []
          },
          {
            stepType: 'SHOP',
            status: 'PENDING',
            confirmedItems: []
          }
        ]
      });
      testDeliveryNoteId = deliveryNote._id.toString();
    });

    it('should transition from PENDING_REVIEW to FINALIZED', async () => {
      const deliveryNote = await DeliveryNote.findById(testDeliveryNoteId);
      expect(deliveryNote?.status).toBe('PENDING_REVIEW');

      // Update status to FINALIZED
      await DeliveryNote.findByIdAndUpdate(testDeliveryNoteId, {
        status: 'FINALIZED',
        reviewed: true
      });

      const updatedDeliveryNote = await DeliveryNote.findById(testDeliveryNoteId);
      expect(updatedDeliveryNote?.status).toBe('FINALIZED');
      expect(updatedDeliveryNote?.reviewed).toBe(true);
    });

    it('should track handover flow progression', async () => {
      const deliveryNote = await DeliveryNote.findById(testDeliveryNoteId);

      // Simulate dispatch handover
      await DeliveryNote.findByIdAndUpdate(testDeliveryNoteId, {
        'handoverFlow.0.status': 'SIGNED',
        'handoverFlow.0.signedAt': new Date(),
        'handoverFlow.0.confirmedItems': [{
          itemId: new Types.ObjectId(testIngredientId),
          confirmedQty: 10
        }]
      });

      let updatedNote = await DeliveryNote.findById(testDeliveryNoteId);
      expect(updatedNote?.handoverFlow[0].status).toBe('SIGNED');
      expect(updatedNote?.handoverFlow[0].confirmedItems).toHaveLength(1);
      expect(updatedNote?.handoverFlow[1].status).toBe('PENDING'); // Driver still pending

      // Simulate driver handover
      await DeliveryNote.findByIdAndUpdate(testDeliveryNoteId, {
        'handoverFlow.1.status': 'SIGNED',
        'handoverFlow.1.signedAt': new Date(),
        'handoverFlow.1.confirmedItems': [{
          itemId: new Types.ObjectId(testIngredientId),
          confirmedQty: 10
        }]
      });

      updatedNote = await DeliveryNote.findById(testDeliveryNoteId);
      expect(updatedNote?.handoverFlow[1].status).toBe('SIGNED');
      expect(updatedNote?.handoverFlow[2].status).toBe('PENDING'); // Shop still pending
    });
  });
});
