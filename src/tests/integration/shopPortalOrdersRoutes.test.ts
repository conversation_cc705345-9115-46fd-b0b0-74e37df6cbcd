import mongoose, { Types } from 'mongoose';
import { NextRequest } from 'next/server';
import Order from '@/models/Order';
import BranchInventory from '@/models/BranchInventory';
import Location from '@/models/Location';
import { Ingredient } from '@/models/Ingredient';
import UOM from '@/models/UOM';
import dbConnect from '@/lib/db';
import { AuthUser } from '@/lib/auth-helpers';

// Mock constants
const MOCK_COMPANY_ID = new mongoose.Types.ObjectId().toString();
const MOCK_SHOP_LOCATION_ID = new mongoose.Types.ObjectId().toString();
const MOCK_CENTRAL_KITCHEN_ID = new mongoose.Types.ObjectId().toString();
const MOCK_USER_ID = new mongoose.Types.ObjectId().toString();

// Mock auth helper
const mockRequireAuth: jest.Mock<Promise<AuthUser>> = jest.fn(async () => ({
  id: MOCK_USER_ID,
  email: '<EMAIL>',
  role: 'shop_manager',
  tenantId: MOCK_COMPANY_ID,
  metadata: {}
}));

jest.mock('@/lib/auth-helpers', () => ({
  requireAuth: mockRequireAuth,
  withAuth: (handler: any) => handler,
}));

describe('Shop Portal Orders API', () => {
  const companyId = MOCK_COMPANY_ID;
  const shopLocationId = MOCK_SHOP_LOCATION_ID;
  const centralKitchenId = MOCK_CENTRAL_KITCHEN_ID;
  let testIngredientId: string;
  let testUomId: string;
  let testBranchInventoryId: string;

  beforeAll(async () => {
    await dbConnect();

    // Create test UOM
    const uom = await UOM.create({
      companyId,
      name: 'Kilogram',
      shortCode: 'kg',
      system: 'metric',
      baseType: 'mass',
      factorToCanonical: 1
    });
    testUomId = uom._id.toString();

    // Create test ingredient
    const ingredient = await Ingredient.create({
      companyId,
      name: 'Test Flour',
      description: 'Test ingredient for shop orders',
      category: 'Dry Goods',
      SKU: 'TEST-FLOUR-001',
      baseUomId: uom._id,
      isActive: true
    });
    testIngredientId = ingredient._id.toString();

    // Create shop location
    await Location.create({
      _id: new Types.ObjectId(shopLocationId),
      companyId,
      name: 'Test Shop Location',
      locationType: 'RETAIL_SHOP',
      canSellToExternal: false,
      canDoTransfers: true,
      canBuyfromExternalSuppliers: false,
      address: '123 Shop St',
      isActive: true
    });

    // Create central kitchen location
    await Location.create({
      _id: new Types.ObjectId(centralKitchenId),
      companyId,
      name: 'Test Central Kitchen',
      locationType: 'CENTRAL_KITCHEN',
      canSellToExternal: true,
      canDoTransfers: true,
      canBuyfromExternalSuppliers: true,
      address: '456 Kitchen Ave',
      isActive: true
    });

    // Create branch inventory item at central kitchen
    const branchInventory = await BranchInventory.create({
      companyId,
      locationId: new Types.ObjectId(centralKitchenId),
      itemId: ingredient._id,
      itemType: 'INGREDIENT',
      baseUomId: uom._id,
      currentStock: 100,
      minOrderQuantity: 5,
      maxOrderQuantity: 50,
      costBasis: 10.50,
      isOrderable: true,
      isActive: true
    });
    testBranchInventoryId = branchInventory._id.toString();
  });

  afterAll(async () => {
    await mongoose.connection.dropDatabase();
    await mongoose.connection.close();
  });

  beforeEach(() => {
    jest.clearAllMocks();
    mockRequireAuth.mockResolvedValue({
      id: MOCK_USER_ID,
      email: '<EMAIL>',
      role: 'shop_manager',
      tenantId: MOCK_COMPANY_ID,
      metadata: {}
    });
  });

  describe('POST /api/company/[companyId]/shop-portal/orders', () => {
    it('should create a valid shop order successfully', async () => {
      const orderData = {
        items: [{
          itemId: testIngredientId,
          branchInventoryId: testBranchInventoryId,
          quantity: 10,
          unitPrice: 10.50,
          notes: 'Test order item'
        }],
        requestedDeliveryDate: new Date(Date.now() + 86400000).toISOString(), // Tomorrow
        notes: 'Test shop order',
        priority: 'NORMAL' as const
      };

      const url = `http://localhost/api/company/${companyId}/shop-portal/orders`;
      const req = new NextRequest(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-location-id': shopLocationId
        },
        body: JSON.stringify(orderData)
      });

      const { POST } = await import('@/app/api/company/[companyId]/shop-portal/orders/route');
      const response = await POST(req, { params: Promise.resolve({ companyId }) });
      const result = await response.json();

      expect(response.status).toBe(201);
      expect(result.success).toBe(true);
      expect(result.message).toBe('Order created successfully');
      expect(result.data).toHaveProperty('orderId');
      expect(result.data).toHaveProperty('orderNumber');
      expect(result.data.orderNumber).toMatch(/^SH-\d{6}$/);
      expect(result.data.status).toBe('CONFIRMED'); // Auto-approved for small orders
      expect(result.data.approvalStatus).toBe('AUTO_APPROVED');
      expect(result.data.totalAmount).toBe(105); // 10 * 10.50
    });

    it('should require authentication', async () => {
      mockRequireAuth.mockRejectedValue(new Error('Unauthorized'));

      const orderData = {
        items: [{
          itemId: testIngredientId,
          branchInventoryId: testBranchInventoryId,
          quantity: 10
        }]
      };

      const url = `http://localhost/api/company/${companyId}/shop-portal/orders`;
      const req = new NextRequest(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-location-id': shopLocationId
        },
        body: JSON.stringify(orderData)
      });

      const { POST } = await import('@/app/api/company/[companyId]/shop-portal/orders/route');
      const response = await POST(req, { params: Promise.resolve({ companyId }) });

      expect(response.status).toBe(500); // Auth error results in 500
    });

    it('should reject unauthorized company access', async () => {
      mockRequireAuth.mockResolvedValue({
        id: MOCK_USER_ID,
        email: '<EMAIL>',
        role: 'shop_manager',
        tenantId: 'different-company-id',
        metadata: {}
      });

      const orderData = {
        items: [{
          itemId: testIngredientId,
          branchInventoryId: testBranchInventoryId,
          quantity: 10
        }]
      };

      const url = `http://localhost/api/company/${companyId}/shop-portal/orders`;
      const req = new NextRequest(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-location-id': shopLocationId
        },
        body: JSON.stringify(orderData)
      });

      const { POST } = await import('@/app/api/company/[companyId]/shop-portal/orders/route');
      const response = await POST(req, { params: Promise.resolve({ companyId }) });
      const result = await response.json();

      expect(response.status).toBe(403);
      expect(result.success).toBe(false);
      expect(result.message).toBe('Unauthorized');
    });

    it('should reject empty order items', async () => {
      const orderData = {
        items: [],
        notes: 'Empty order test'
      };

      const url = `http://localhost/api/company/${companyId}/shop-portal/orders`;
      const req = new NextRequest(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-location-id': shopLocationId
        },
        body: JSON.stringify(orderData)
      });

      const { POST } = await import('@/app/api/company/[companyId]/shop-portal/orders/route');
      const response = await POST(req, { params: Promise.resolve({ companyId }) });
      const result = await response.json();

      expect(response.status).toBe(400);
      expect(result.success).toBe(false);
      expect(result.message).toBe('Order must contain at least one item');
    });

    it('should validate minimum order quantity', async () => {
      const orderData = {
        items: [{
          itemId: testIngredientId,
          branchInventoryId: testBranchInventoryId,
          quantity: 2, // Below minimum of 5
          unitPrice: 10.50
        }]
      };

      const url = `http://localhost/api/company/${companyId}/shop-portal/orders`;
      const req = new NextRequest(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-location-id': shopLocationId
        },
        body: JSON.stringify(orderData)
      });

      const { POST } = await import('@/app/api/company/[companyId]/shop-portal/orders/route');
      const response = await POST(req, { params: Promise.resolve({ companyId }) });
      const result = await response.json();

      expect(response.status).toBe(400);
      expect(result.success).toBe(false);
      expect(result.message).toContain('Minimum order quantity');
      expect(result.message).toContain('5');
    });

    it('should validate maximum order quantity', async () => {
      const orderData = {
        items: [{
          itemId: testIngredientId,
          branchInventoryId: testBranchInventoryId,
          quantity: 60, // Above maximum of 50
          unitPrice: 10.50
        }]
      };

      const url = `http://localhost/api/company/${companyId}/shop-portal/orders`;
      const req = new NextRequest(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-location-id': shopLocationId
        },
        body: JSON.stringify(orderData)
      });

      const { POST } = await import('@/app/api/company/[companyId]/shop-portal/orders/route');
      const response = await POST(req, { params: Promise.resolve({ companyId }) });
      const result = await response.json();

      expect(response.status).toBe(400);
      expect(result.success).toBe(false);
      expect(result.message).toContain('Maximum order quantity');
      expect(result.message).toContain('50');
    });

    it('should validate stock availability', async () => {
      const orderData = {
        items: [{
          itemId: testIngredientId,
          branchInventoryId: testBranchInventoryId,
          quantity: 150, // Above available stock of 100
          unitPrice: 10.50
        }]
      };

      const url = `http://localhost/api/company/${companyId}/shop-portal/orders`;
      const req = new NextRequest(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-location-id': shopLocationId
        },
        body: JSON.stringify(orderData)
      });

      const { POST } = await import('@/app/api/company/[companyId]/shop-portal/orders/route');
      const response = await POST(req, { params: Promise.resolve({ companyId }) });
      const result = await response.json();

      expect(response.status).toBe(400);
      expect(result.success).toBe(false);
      expect(result.message).toContain('Insufficient stock');
      expect(result.message).toContain('Available: 100');
    });

    it('should handle large orders requiring manual approval', async () => {
      const orderData = {
        items: [{
          itemId: testIngredientId,
          branchInventoryId: testBranchInventoryId,
          quantity: 50,
          unitPrice: 25.00, // Total: 1250 > 1000 threshold
          notes: 'Large order test'
        }],
        priority: 'URGENT' as const
      };

      const url = `http://localhost/api/company/${companyId}/shop-portal/orders`;
      const req = new NextRequest(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-location-id': shopLocationId
        },
        body: JSON.stringify(orderData)
      });

      const { POST } = await import('@/app/api/company/[companyId]/shop-portal/orders/route');
      const response = await POST(req, { params: Promise.resolve({ companyId }) });
      const result = await response.json();

      expect(response.status).toBe(201);
      expect(result.success).toBe(true);
      expect(result.data.status).toBe('INCOMING'); // Not auto-approved
      expect(result.data.approvalStatus).toBe('PENDING');
      expect(result.data.totalAmount).toBe(1250);
    });

    it('should reject invalid branch inventory item', async () => {
      const invalidBranchInventoryId = new Types.ObjectId().toString();
      const orderData = {
        items: [{
          itemId: testIngredientId,
          branchInventoryId: invalidBranchInventoryId,
          quantity: 10
        }]
      };

      const url = `http://localhost/api/company/${companyId}/shop-portal/orders`;
      const req = new NextRequest(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-location-id': shopLocationId
        },
        body: JSON.stringify(orderData)
      });

      const { POST } = await import('@/app/api/company/[companyId]/shop-portal/orders/route');
      const response = await POST(req, { params: Promise.resolve({ companyId }) });
      const result = await response.json();

      expect(response.status).toBe(400);
      expect(result.success).toBe(false);
      expect(result.message).toContain('is not available for ordering');
    });
  });

  describe('GET /api/company/[companyId]/shop-portal/orders', () => {
    beforeEach(async () => {
      // Clean up orders before each test
      await Order.deleteMany({ companyId });
    });

    it('should fetch shop orders with pagination', async () => {
      // Create test orders
      const orders = [];
      for (let i = 1; i <= 3; i++) {
        const order = await Order.create({
          companyId,
          orderNumber: `SH-${String(i).padStart(6, '0')}`,
          status: 'CONFIRMED',
          buyer: {
            buyerType: 'BRANCH',
            buyerId: shopLocationId,
            buyerName: 'Test Shop Location'
          },
          seller: {
            sellerType: 'CENTRAL_KITCHEN',
            sellerId: centralKitchenId,
            sellerName: 'Test Central Kitchen'
          },
          sellerLocationId: new Types.ObjectId(centralKitchenId),
          buyerLocationId: new Types.ObjectId(shopLocationId),
          items: [{
            itemType: 'INGREDIENT',
            itemId: new Types.ObjectId(testIngredientId),
            description: 'Test Flour',
            quantity: 10,
            deliveredQuantity: 0,
            uomId: new Types.ObjectId(testUomId),
            unitPrice: 10.50,
            lineTotal: 105
          }],
          orderType: 'INTERNAL_TRANSFER',
          totalAmount: 105,
          createdBy: new Types.ObjectId(MOCK_USER_ID)
        });
        orders.push(order);
      }

      const url = `http://localhost/api/company/${companyId}/shop-portal/orders?locationId=${shopLocationId}&page=1&limit=2`;
      const req = new NextRequest(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const { GET } = await import('@/app/api/company/[companyId]/shop-portal/orders/route');
      const response = await GET(req, { params: Promise.resolve({ companyId }) });
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(2);
      expect(result.pagination).toEqual({
        page: 1,
        limit: 2,
        total: 3,
        totalPages: 2
      });
      expect(result.data[0].orderNumber).toBeDefined();
      expect(result.data[0].buyer.buyerType).toBe('BRANCH');
      expect(result.data[0].seller.sellerType).toBe('CENTRAL_KITCHEN');
    });

    it('should filter orders by status', async () => {
      // Create orders with different statuses
      await Order.create({
        companyId,
        orderNumber: 'SH-000001',
        status: 'INCOMING',
        buyer: {
          buyerType: 'BRANCH',
          buyerId: shopLocationId,
          buyerName: 'Test Shop Location'
        },
        seller: {
          sellerType: 'CENTRAL_KITCHEN',
          sellerId: centralKitchenId,
          sellerName: 'Test Central Kitchen'
        },
        sellerLocationId: new Types.ObjectId(centralKitchenId),
        buyerLocationId: new Types.ObjectId(shopLocationId),
        items: [],
        orderType: 'INTERNAL_TRANSFER',
        totalAmount: 50
      });

      await Order.create({
        companyId,
        orderNumber: 'SH-000002',
        status: 'CONFIRMED',
        buyer: {
          buyerType: 'BRANCH',
          buyerId: shopLocationId,
          buyerName: 'Test Shop Location'
        },
        seller: {
          sellerType: 'CENTRAL_KITCHEN',
          sellerId: centralKitchenId,
          sellerName: 'Test Central Kitchen'
        },
        sellerLocationId: new Types.ObjectId(centralKitchenId),
        buyerLocationId: new Types.ObjectId(shopLocationId),
        items: [],
        orderType: 'INTERNAL_TRANSFER',
        totalAmount: 75
      });

      const url = `http://localhost/api/company/${companyId}/shop-portal/orders?locationId=${shopLocationId}&status=confirmed`;
      const req = new NextRequest(url, {
        method: 'GET'
      });

      const { GET } = await import('@/app/api/company/[companyId]/shop-portal/orders/route');
      const response = await GET(req, { params: Promise.resolve({ companyId }) });
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(1);
      expect(result.data[0].status).toBe('CONFIRMED');
      expect(result.data[0].orderNumber).toBe('SH-000002');
    });

    it('should require locationId parameter', async () => {
      const url = `http://localhost/api/company/${companyId}/shop-portal/orders`;
      const req = new NextRequest(url, {
        method: 'GET'
      });

      const { GET } = await import('@/app/api/company/[companyId]/shop-portal/orders/route');
      const response = await GET(req, { params: Promise.resolve({ companyId }) });
      const result = await response.json();

      expect(response.status).toBe(400);
      expect(result.success).toBe(false);
      expect(result.message).toBe('Location ID is required');
    });
  });
});
