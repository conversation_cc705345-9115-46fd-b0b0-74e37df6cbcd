// src/app/(auth)/login/page.tsx
'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/lib/auth';  // Fixed import path
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const { signIn, loading, userData } = useAuth();
  const router = useRouter();

  useEffect(() => {
    console.log('Login page useEffect triggered with:', {
      loading,
      userDataExists: !!userData,
      userType: userData?.userType,
      companyId: userData && userData.userType === 'company_user' ? userData.companyId : undefined
    });

    if (!loading && userData) {
      console.log('Attempting redirect with userType:', userData.userType);
      if (userData.userType === 'company_user' && 'companyId' in userData && userData.companyId) {
        const redirectPath = `/company/${userData.companyId}/dashboard`;
        console.log('Redirecting to company dashboard:', redirectPath);
        router.replace(redirectPath);
      } else if (userData.userType === 'superuser') {
        console.log('Redirecting to superuser dashboard');
        router.replace('/superadmin/dashboard');
      }
    }
  }, [loading, userData, router]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    try {
      await signIn(email, password);
      // Redirection will happen automatically based on user type
    } catch (error: unknown) {
      console.error('Login error:', error);
      setError(error instanceof Error ? error.message : 'An unknown error occurred');
    }
  };



  if (loading) {
    return (
      <main className="flex min-h-screen flex-col items-center justify-center p-24">
        <div className="text-center">
          <div className="text-lg text-gray-600">Loading...</div>
        </div>
      </main>
    );
  }

  return (
    <main className="flex min-h-screen flex-col items-center justify-between p-24">
      {/* Header */}
      <div className="z-10 max-w-5xl w-full items-center justify-between font-mono text-sm lg:flex">
        <h1 className="text-4xl font-bold">FoodPrepAI Web Dashboard</h1>
      </div>

      {/* Login Form */}
      <div className="relative flex place-items-center">
        <div className="w-full max-w-md space-y-8">
          <div className="text-center">
            <h2 className="text-2xl font-semibold mb-2">
              Central Kitchen Management Platform
            </h2>
            <p className="text-sm opacity-50 mb-8">
              Sign in to access your dashboard
            </p>
          </div>

          <form className="space-y-6" onSubmit={handleSubmit}>
            {error && (
              <div className="rounded-lg border border-red-200 bg-red-50 p-4">
                <div className="text-sm text-red-700">{error}</div>
              </div>
            )}

            <div className="space-y-4">
              <div>
                <Input
                  type="email"
                  required
                  placeholder="Email address"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="h-12 text-base"
                />
              </div>
              <div>
                <Input
                  type="password"
                  required
                  placeholder="Password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="h-12 text-base"
                />
              </div>
            </div>

            <div>
              <Button
                type="submit"
                className="w-full h-12 text-base font-semibold transition-colors hover:bg-gray-800"
                disabled={loading}
              >
                {loading ? 'Signing in...' : 'Sign in'}
              </Button>
            </div>
          </form>
        </div>
      </div>

      {/* Footer spacer */}
      <div className="mb-32"></div>
    </main>
  );
}
